package com.chargebolt.pheidi.dto;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LogMsgDTO {
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 业务模块
     */
    private String module;
    /**
     * 主题/问题
     */
    private String subject;
    /**
     * 日志内容
     */
    private String logMsg;
    /**
     * 日志时间
     */
    private String logTime;

    /**
     * 扩展信息
     */
    private Map<String,String> extra;
}
