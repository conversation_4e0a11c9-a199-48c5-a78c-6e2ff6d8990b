package com.chargebolt.pheidi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PheidiResponse {

    private boolean success;
    private String code;
    private String message;
    private Long responseTime;

    public static PheidiResponse success() {
        return PheidiResponse.builder()
                .success(true)
                .code("0")
                .message("ok")
                .responseTime(System.currentTimeMillis())
                .build();
    }

    public static PheidiResponse error(String message) {
        return PheidiResponse.builder()
                .success(false)
                .code("-1")
                .message(message)
                .responseTime(System.currentTimeMillis())
                .build();
    }
}
