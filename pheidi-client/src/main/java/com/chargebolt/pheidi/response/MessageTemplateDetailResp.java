package com.chargebolt.pheidi.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.util.List;

@Data
@Tag(name = "消息模板详情响应")
public class MessageTemplateDetailResp {
    @Schema(description = "模板id")
    private Long templateId;
    @Schema(description = "推送渠道")
    private List<Integer> channel;
    @Schema(description = "模板code")
    private String code;
    @Schema(description = "消息内容")
    private String content;
    @Schema(description = "模板名称")
    private String name;
    @Schema(description = "跳转目标")
    private Integer redirectGoal;
    @Schema(description = "跳转链接")
    private String redirectLink;
    @Schema(description = "消息标题")
    private String title;
    @Schema(description = "业务类型")
    private Integer type;
}
