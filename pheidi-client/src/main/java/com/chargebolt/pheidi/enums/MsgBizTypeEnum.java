package com.chargebolt.pheidi.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import com.chargebolt.pheidi.dto.*;

import lombok.Getter;

/**
 * 业务类型枚举
 */
@Getter
public enum MsgBizTypeEnum {
    SYSTEM(1, "系统", "https://img-sg.chargebolt.com/chargebolt/file/202503/zoozgzogv0qgbnj9.png"),
    STORE(2, "门店", "https://img-sg.chargebolt.com/chargebolt/file/202503/dr7rc7911zj4ac5b.png"),
    MERCHANT(3, "商家", "https://img-sg.chargebolt.com/chargebolt/file/202503/disuau6vq7qqljg0.png"),
    DEVICE(4, "设备", "https://img-sg.chargebolt.com/chargebolt/file/202503/ratgzbz5gkghbsl1.png"),
    ;

    private final Integer code;
    private final String desc;
    private final String iconUrl;

    MsgBizTypeEnum(Integer code, String desc, String iconUrl) {
        this.code = code;
        this.desc = desc;
        this.iconUrl = iconUrl;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static MsgBizTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取所有的枚举项，用于下拉选择
     *
     * @return 下拉选择项
     */
    public static List<MapDTO> getSelectItems() {
        return Arrays.stream(values())
                .map(item -> new MapDTO(item.getCode(), item.getDesc()))
                .collect(Collectors.toList());
    }
}
