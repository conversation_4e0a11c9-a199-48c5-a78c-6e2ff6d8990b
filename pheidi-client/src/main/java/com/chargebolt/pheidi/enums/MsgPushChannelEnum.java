package com.chargebolt.pheidi.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import com.chargebolt.pheidi.dto.*;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 推送通道枚举
 */
@Getter
@AllArgsConstructor
public enum MsgPushChannelEnum {
    IN_APP(1, "inApp", "App内推送"),
    ONE_SIGNAL(2, "oneSignal", "OneSignal推送"),
    ;

    private final Integer code;
    private final String beanName;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static MsgPushChannelEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取所有的枚举项，用于下拉选择
     *
     * @return 下拉选择项
     */
    public static List<MapDTO> getSelectItems() {
        return Arrays.stream(values())
                .map(item -> new MapDTO(item.getCode(), item.getDesc()))
                .collect(Collectors.toList());
    }

    /**
     * 根据渠道类型获取对应的策略实现类bean名称
     *
     * @param channel 渠道类型
     * @return 策略实现类bean名称
     */
    public static String getBeanName(Integer channel) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(channel))
                .findFirst()
                .map(MsgPushChannelEnum::getBeanName)
                .orElseThrow(() -> new IllegalArgumentException("Invalid channel type: " + channel));
    }

    /**
     * 根据渠道类型获取描述
     *
     * @param channel 渠道类型
     * @return 渠道描述
     */
    public static String getDesc(Integer channel) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(channel))
                .findFirst()
                .map(MsgPushChannelEnum::getDesc)
                .orElse("未知渠道");
    }
}
