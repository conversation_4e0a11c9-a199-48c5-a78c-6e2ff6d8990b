package com.chargebolt.pheidi.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TemplateStatusEnum {

    ENABLE(1, "启用"),
    DISABLE(2, "禁用"),
    ;

    private Integer status;
    private String desc;

    public static String getDesc(Integer status) {
        for (TemplateStatusEnum item : TemplateStatusEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
