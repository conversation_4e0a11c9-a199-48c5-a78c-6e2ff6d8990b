package com.chargebolt.pheidi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.List;
import com.google.common.collect.Lists;

@Getter
@AllArgsConstructor
public enum TemplateCodeParamsEnum {

    device_full("device_full", Lists.newArrayList(ParamEnum.shop_name, ParamEnum.device_no, ParamEnum.shop_id)),
    device_lack("device_lack",
            Lists.newArrayList(ParamEnum.shop_name, ParamEnum.device_no, ParamEnum.chargebank_num, ParamEnum.shop_id)),
    device_offline("device_offline",
            Lists.newArrayList(ParamEnum.shop_name, ParamEnum.device_no, ParamEnum.offline_time, ParamEnum.shop_id)),
            ;

    private String templateCode;
    private List<ParamEnum> paramList;

    public static TemplateCodeParamsEnum getByTemplateCode(String templateCode) {
        for (TemplateCodeParamsEnum templateCodeParamsEnum : TemplateCodeParamsEnum.values()) {
            if (templateCodeParamsEnum.getTemplateCode().equals(templateCode)) {
                return templateCodeParamsEnum;
            }
        }
        return null;
    }

    @Getter
    @AllArgsConstructor
    public enum ParamEnum {
        shop_id("shopId", "门店id"),
        shop_name("shopName", "店铺名称"),
        device_no("deviceNo", "设备编号"),
        chargebank_num("powerbankNum", "充电宝数量"),
        offline_time("offlineTime", "离线时间"),
        ;

        private String param;
        private String desc;
    }

}
