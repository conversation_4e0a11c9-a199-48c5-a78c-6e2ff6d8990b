package com.chargebolt.pheidi.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.chargebolt.pheidi.dto.MapDTO;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RedirectGoalEnum {

    MESSAGE_CENTER(0, "消息中心"),
    BUSINESS_DETAIL(1, "业务详情"),
    ;

    private Integer code;
    private String desc;

    public static String getDesc(Integer code) {
        for (RedirectGoalEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 获取所有的枚举项，用于下拉选择
     *
     * @return 下拉选择项
     */
    public static List<MapDTO> getSelectItems() {
        return Arrays.stream(values())
                .map(item -> new MapDTO(item.getCode(), item.getDesc()))
                .collect(Collectors.toList());
    }

}
