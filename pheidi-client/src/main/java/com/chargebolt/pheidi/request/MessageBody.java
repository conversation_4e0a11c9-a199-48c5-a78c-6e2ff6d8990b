package com.chargebolt.pheidi.request;

import lombok.Data;

import java.util.Map;

@Data
public class MessageBody {

    /**
     * 接收者Id
     */
    private String targetId;
    /**
     * 接收类型
     */
    private Integer targetType;
    /**
     * 消息模板code
     */
    private String templateCode;
    /**
     * 消息参数
     */
    private Map<String, String> contentVariable;
    /**
     * 生成时间
     */
    private Long createTime;
}
