erDiagram
    message ||--o{ message_channel : "1:N"
    message ||--o{ message_read_status : "1:N"
    message }|--|| message_template : "N:1"

    message {
        BIGINT id PK
        TINYINT biz_type
        TINYINT status
        TINYINT priority
        DATETIME expire_time
        TINYINT target_type
        VARCHAR target_value
        VARCHAR title
        VARCHAR content
        VARCHAR redirect_url
        TINYINT redirect_goal
        DATETIME generate_time
        BIGINT template_id FK
        BIGINT gmt_create
        BIGINT gmt_update
        TINYINT deleted
    }
    message_channel {
        BIGINT id PK
        BIGINT message_id FK
        INT channel
        TINYINT status
        INT retry_count
        DATETIME last_attempt_time
        VARCHAR error_msg
        TINYINT target_type
        VARCHAR target_value
        DATETIME scheduled_time
        DATETIME completed_time
        BIGINT gmt_create
        BIGINT gmt_update
        TINYINT deleted
    }
    message_read_status {
        BIGINT id PK
        BIGINT message_id FK
        BIGINT user_id
        DATETIME read_time
        INT channel
        BIGINT gmt_create
        BIGINT gmt_update
        TINYINT deleted
    }
    message_template {
        BIGINT id PK
        VARCHAR name
        VARC<PERSON>R code
        TINYINT biz_type
        TINYINT status
        VARCHAR channels
        VARCHAR title
        VARCHAR content
        VARCHAR redirect_url
        TINYINT redirect_goal
        BIGINT gmt_create
        BIGINT gmt_update
        TINYINT deleted
    }