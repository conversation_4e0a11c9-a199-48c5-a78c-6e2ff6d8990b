sequenceDiagram
    participant App
    participant PushService
    participant PushRecord
    participant Message

    App->>PushService: 触发推送(messageId, channel)
    PushService->>PushRecord: 创建记录(status=PENDING)
    PushService->>Message: 读取消息内容
    PushService->>ThirdParty: 调用推送API
    ThirdParty-->>PushService: 返回结果
    PushService->>PushRecord: 更新状态(SENT/FAILED)
    PushService->>PushRecord: 记录错误信息（如失败）