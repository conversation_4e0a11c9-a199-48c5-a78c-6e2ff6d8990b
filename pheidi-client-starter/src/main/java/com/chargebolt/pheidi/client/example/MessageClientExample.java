package com.chargebolt.pheidi.client.example;

import com.chargebolt.pheidi.client.MessageClient;
import com.chargebolt.pheidi.client.template.MessageTemplateClient;
import com.chargebolt.pheidi.client.util.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import com.chargebolt.pheidi.dto.LogMsgDTO;
import java.time.*;
import java.time.format.DateTimeFormatter;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 消息客户端示例
 * 展示不同的使用方式
 */
@Slf4j
@Component
public class MessageClientExample implements CommandLineRunner {

    @Autowired
    private MessageClient messageClient;
    
    @Autowired
    private MessageTemplateClient templateClient;

    @Override
    public void run(String... args) {
        // 示例1：直接使用 MessageClient
        // directClientExample();  
    }
    
    /**
     * 示例1：直接使用 MessageClient
     */
    private void directClientExample() {
        LogMsgDTO dto = new LogMsgDTO();
        
        dto.setModule("押金退款"); // 从参数中传入
        dto.setSubject("押金自动退回失败"); // 从参数中传入
        dto.setLogMsg("余额不足"); // 从参数中传入

        dto.setAppName("Hera");// 从配置中读取
        dto.setLogTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 从参数中传入对象或者 map
        dto.setExtra(new HashMap<String,String>(){{
            put("agentId", "0");
            put("userId", "28383");
            put("orderNo", "IDT122504160004068048704103");
        }});
        
        try {
            // 异步发送消息
            CompletableFuture<Boolean> future = messageClient.sendMessage(dto.getModule(), dto.getSubject(), dto.getLogMsg(), dto.getExtra());
            
            // 处理结果
            future.thenAccept(result -> {
                if (result) {
                    log.info("消息发送成功");
                } else {
                    log.error("消息发送失败");
                }
            }).exceptionally(ex -> {
                log.error("消息发送异常: {}", ex.getMessage(), ex);
                return null;
            });
            
            // 带重试的消息发送
            CompletableFuture<Boolean> retryFuture = messageClient.sendMessageWithRetry(dto.getModule(), dto.getSubject(), dto.getLogMsg(), dto.getExtra());
            retryFuture.thenAccept(result -> log.info("带重试的消息发送结果: {}", result));
        } catch (Exception e) {
            log.error("消息客户端示例异常: {}", e.getMessage(), e);
        }
    }

}
