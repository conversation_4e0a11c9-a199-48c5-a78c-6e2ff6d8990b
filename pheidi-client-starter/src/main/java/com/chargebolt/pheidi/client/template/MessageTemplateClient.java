package com.chargebolt.pheidi.client.template;

import com.chargebolt.pheidi.client.MessageClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 基于模板的消息客户端
 * 提供针对不同业务场景的便捷方法
 */
@Slf4j
@Component
public class MessageTemplateClient {

    @Autowired
    private MessageClient messageClient;

    /**
     * 退款模块
     *
     * @param subject 用户ID
     * @param errMsg 用户名
     * @return 发送结果
     */
    public CompletableFuture<Boolean> refundReport(String subject, String errMsg,
            Map<String, String> params) {
        return messageClient.sendMessageWithRetry("退款模块", subject, errMsg, params);
    }

    /**
     * 支付模块
     *
     * @param subject 用户ID
     * @param errMsg 用户名
     * @return 发送结果
     */
    public CompletableFuture<Boolean> payReport(String subject, String errMsg,
            Map<String, String> params) {
        return messageClient.sendMessageWithRetry("支付模块", subject, errMsg, params);
    }

    /**
     * 登录模块
     *
     * @param subject 用户ID
     * @param errMsg 用户名
     * @return 发送结果
     */
    public CompletableFuture<Boolean> loginReport(String subject, String errMsg,
            Map<String, String> params) {
        return messageClient.sendMessageWithRetry("登录模块", subject, errMsg, params);
        
    }

    /**
     * 设备模块
     *
     * @param subject 用户ID
     * @param errMsg 用户名
     * @return 发送结果
     */
    public CompletableFuture<Boolean> deviceReport(String subject, String errMsg,
            Map<String, String> params) {
        return messageClient.sendMessageWithRetry("设备模块", subject, errMsg, params);
    }
}
