package com.chargebolt.pheidi.client.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.chargebolt.pheidi.client.MessageClient;
import com.chargebolt.pheidi.client.config.MessageClientProperties;
import com.chargebolt.pheidi.client.exception.MessageClientException;
import com.chargebolt.pheidi.client.remote.AppMonitorApi;
import com.chargebolt.pheidi.dto.LogMsgDTO;
import com.chargebolt.pheidi.dto.PheidiResponse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestClientException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 消息客户端默认实现
 */
@Slf4j
public class DefaultMessageClient implements MessageClient {

    private final MessageClientProperties properties;
    private final AppMonitorApi appMonitorApi;
    private final Executor executor;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public DefaultMessageClient(MessageClientProperties properties,
            AppMonitorApi appMonitorApi,
            Executor executor) {
        this.properties = properties;
        this.appMonitorApi = appMonitorApi;
        this.executor = executor;
    }

    @Override
    @Async("messageClientExecutor")
    public CompletableFuture<Boolean> sendMessage(String module, String subject, String errMsg, Map<String, String> params) {
            if (properties.isLogEnabled()) {
                log.info("开始发送消息: module={}, subject={}, errMsg={}, params={}", module, subject, errMsg, params);
            }
            // 构建消息体
            LogMsgDTO messageBody = new LogMsgDTO();
            messageBody.setModule(module);
            messageBody.setSubject(subject);
            messageBody.setLogMsg(errMsg);
            messageBody.setAppName(properties.getAppName());
            messageBody.setLogTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            messageBody.setExtra(params);
            return CompletableFuture.completedFuture(doSendMessage(messageBody));
    }

    @Override
    @Async("messageClientExecutor")
    @Retryable(value = { RestClientException.class,
            MessageClientException.class }, maxAttemptsExpression = "#{${message.client.maxRetries:3}}", backoff = @Backoff(delayExpression = "#{${message.client.retryInterval:1000}}"))
    public CompletableFuture<Boolean> sendMessageWithRetry(String module, String subject, String errMsg, Map<String, String> params) {
        if (properties.isLogEnabled()) {
            log.info("开始发送消息（带重试）: module={}, subject={}, errMsg={}, params={}", module, subject, errMsg, params);
        }
        // 构建消息体
        LogMsgDTO messageBody = new LogMsgDTO();
        messageBody.setModule(module);
        messageBody.setSubject(subject);
        messageBody.setLogMsg(errMsg);
        messageBody.setAppName(properties.getAppName());
        messageBody.setLogTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        messageBody.setExtra(params);
        return CompletableFuture.completedFuture(doSendMessage(messageBody));
    }

    /**
     * 执行消息发送
     */
    private boolean doSendMessage(LogMsgDTO messageBody) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("消息发送");
        try {
            PheidiResponse response = appMonitorApi.logPush(messageBody);
            if (properties.isLogEnabled()) {
                try {
                    log.info("消息发送响应: result={}", objectMapper.writeValueAsString(response));
                } catch (Exception e) {
                    log.info("消息发送响应: result={}", response);
                }
            }

            if (response != null && "0".equals(response.getCode())) {
                return true;
            }
            try {
                log.warn("消息发送失败: result = {}", objectMapper.writeValueAsString(response));
            } catch (Exception e) {
                log.warn("消息发送失败: result = {}", response);
            }
            return false;
        } catch (Exception e) {
            log.error("消息发送请求异常: {}", e.getMessage(), e);
            // 抛出异常以触发重试
            throw new MessageClientException("消息发送请求异常", e);
        } finally{
            stopWatch.stop();
            if (properties.isLogEnabled()) {
                log.info("消息发送耗时: {}ms", stopWatch.getTotalTimeMillis());
            }
        }

    }
}
