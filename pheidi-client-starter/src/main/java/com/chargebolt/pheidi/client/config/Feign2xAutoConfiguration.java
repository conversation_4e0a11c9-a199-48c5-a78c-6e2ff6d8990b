package com.chargebolt.pheidi.client.config;

import feign.Client;
import feign.Contract;
import feign.Feign;
import feign.Request;
import feign.Retryer;
import feign.codec.Decoder;
import feign.codec.Encoder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.chargebolt.pheidi.client.remote.AppMonitorApi;

import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

/**
 * Feign客户端自动配置类
 * 用于配置Feign客户端相关Bean及拦截器
 * 
 * <AUTHOR>
 * @since 2021/6/7
 **/
@ConditionalOnClass(FeignClientsConfiguration.class)
@Configuration
@Import(FeignClientsConfiguration.class)
public class Feign2xAutoConfiguration {

    @Resource
    private MessageClientProperties messageClientProperties;

    private static final String URL = "http://pheidi:8080";

    /**
     * 创建AppMonitorApi接口的Feign客户端
     * 集成授权头和监控功能
     * 
     * @param contract Feign契约
     * @param decoder  响应解码器
     * @param encoder  请求编码器
     * @return AppMonitorApi实例
     */
    @Bean
    @ConditionalOnMissingBean(AppMonitorApi.class)
    public AppMonitorApi appMonitorApi(Contract contract, Decoder decoder, Encoder encoder) {
        Client client = new Client.Default(null, null);
        return Feign.builder()
                .client(client)
                .options(new Request.Options(messageClientProperties.getConnectTimeout(), TimeUnit.MILLISECONDS,
                        messageClientProperties.getReadTimeout(), TimeUnit.MILLISECONDS, true))
                .contract(contract)
                .decoder(decoder)
                .encoder(encoder)
                .retryer(Retryer.NEVER_RETRY)
                .requestInterceptor(requestTemplate -> {
                    requestTemplate.header("Authorization", messageClientProperties.getAppKey());
                })
                .target(AppMonitorApi.class, URL);
    }
}
