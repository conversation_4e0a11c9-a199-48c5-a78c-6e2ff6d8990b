package com.chargebolt.pheidi.client.remote;

import com.chargebolt.pheidi.dto.LogMsgDTO;
import com.chargebolt.pheidi.dto.PheidiResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2025/4/30
 **/
public interface AppMonitorApi {

    @PostMapping("/monitor/log/push")
    PheidiResponse logPush(@RequestBody LogMsgDTO logMsgDTO);
}
