package com.chargebolt.pheidi.client.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 消息客户端配置属性
 */
@Data
@ConfigurationProperties(prefix = "message.client")
public class MessageClientProperties {
    /**
     * 应用名称
     */
    private String appName = "unknown";

    /**
     * 应用Key
     */
    private String appKey = "unknown";
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 3000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 5000;
    
    /**
     * 最大重试次数
     */
    private int maxRetries = 3;
    
    /**
     * 重试间隔（毫秒）
     */
    private long retryInterval = 1000;
    
    /**
     * 是否启用日志
     */
    private boolean logEnabled = true;
    
    /**
     * 线程池配置
     */
    private ThreadPoolProperties threadPool = new ThreadPoolProperties();
    
    /**
     * 线程池配置属性
     */
    @Data
    public static class ThreadPoolProperties {
        /**
         * 核心线程数
         */
        private int corePoolSize = 5;
        
        /**
         * 最大线程数
         */
        private int maxPoolSize = 10;
        
        /**
         * 队列容量
         */
        private int queueCapacity = 100;
        
        /**
         * 线程名称前缀
         */
        private String threadNamePrefix = "message-client-";
    }
}
