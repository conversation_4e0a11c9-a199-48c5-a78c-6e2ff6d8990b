package com.chargebolt.pheidi.client.config;

import com.chargebolt.pheidi.client.MessageClient;
import com.chargebolt.pheidi.client.impl.DefaultMessageClient;
import com.chargebolt.pheidi.client.remote.AppMonitorApi;
import com.chargebolt.pheidi.client.template.MessageTemplateClient;
import com.chargebolt.pheidi.client.util.MessageSender;

import feign.Feign;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PreDestroy;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 消息客户端自动配置
 */
@Configuration
@EnableConfigurationProperties(MessageClientProperties.class)
@EnableRetry
@EnableAsync
@Import({MessageTemplateClient.class, Feign2xAutoConfiguration.class})
public class MessageClientAutoConfiguration {

    private ThreadPoolTaskExecutor messageThreadPoolExecutor;

    /**
     * 配置消息客户端线程池
     */
    @Bean("messageClientExecutor")
    public Executor messageClientExecutor(MessageClientProperties properties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(properties.getThreadPool().getCorePoolSize());
        executor.setMaxPoolSize(properties.getThreadPool().getMaxPoolSize());
        executor.setQueueCapacity(properties.getThreadPool().getQueueCapacity());
        executor.setThreadNamePrefix(properties.getThreadPool().getThreadNamePrefix());
        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        
        // 保存引用以便在销毁时关闭
        this.messageThreadPoolExecutor = executor;
        
        return executor;
    }

    /**
     * 配置消息客户端
     */
    @Bean
    @ConditionalOnMissingBean
    public MessageClient messageClient(MessageClientProperties properties, 
                                      AppMonitorApi appMonitorApi,
                                      Executor messageClientExecutor) {
        DefaultMessageClient client = new DefaultMessageClient(properties, appMonitorApi, messageClientExecutor);
        // 初始化静态工具类
        MessageSender.init(client);
        return client;
    }
    
    /**
     * 应用关闭时优雅关闭线程池
     */
    @PreDestroy
    public void shutdown() {
        if (messageThreadPoolExecutor != null) {
            messageThreadPoolExecutor.shutdown();
        }
    }
}
