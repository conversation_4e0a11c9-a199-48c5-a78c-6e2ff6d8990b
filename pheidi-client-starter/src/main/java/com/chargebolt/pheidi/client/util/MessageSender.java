package com.chargebolt.pheidi.client.util;

import com.chargebolt.pheidi.client.MessageClient;
import com.chargebolt.pheidi.client.exception.MessageClientException;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 消息发送工具类
 * 提供静态方法便于直接调用
 */
@Slf4j
public class MessageSender {

    private static MessageClient messageClient;

    /**
     * 初始化客户端
     */
    public static void init(MessageClient client) {
        messageClient = client;
    }

    /**
     * 发送消息
     *
     * @param module  模块名称
     * @param subject 消息主题
     * @param errMsg  错误信息
     * @param params  消息参数
     * @return 发送结果
     */
    public static CompletableFuture<Boolean> send(String module, String subject, String errMsg,
            Map<String, String> params) {
        checkClient();
        return messageClient.sendMessage(module, subject, errMsg, params);
    }

    /**
     * 发送消息
     *
     * @param module  模块名称
     * @param subject 消息主题
     * @param errMsg  错误信息
     * @return 发送结果
     */
    public static CompletableFuture<Boolean> send(String module, String subject, String errMsg) {
        checkClient();
        return messageClient.sendMessage(module, subject, errMsg, Maps.newHashMap());
    }

    /**
     * 发送消息（带重试）
     *
     * @param module  模块名称
     * @param subject 消息主题
     * @param errMsg  错误信息
     * @param params  消息参数
     * @return 发送结果
     */
    public static CompletableFuture<Boolean> sendWithRetry(String module, String subject, String errMsg,
            Map<String, String> params) {
        checkClient();
        return messageClient.sendMessageWithRetry(module, subject, errMsg, params);
    }

    /**
     * 发送消息（使用对象参数）
     *
     * @param module  模块名称
     * @param subject 消息主题
     * @param errMsg  错误信息
     * @param params  对象参数，将自动提取字段名和值作为消息参数
     * @return 发送结果
     */
    public static CompletableFuture<Boolean> send(String module, String subject, String errMsg, Object params) {
        checkClient();
        return messageClient.sendMessage(module, subject, errMsg, params);
    }

    /**
     * 发送消息（带重试，使用对象参数）
     *
     * @param module  模块名称
     * @param subject 消息主题
     * @param errMsg  错误信息
     * @param params  对象参数，将自动提取字段名和值作为消息参数
     * @return 发送结果
     */
    public static CompletableFuture<Boolean> sendWithRetry(String module, String subject, String errMsg,
            Object params) {
        checkClient();
        return messageClient.sendMessageWithRetry(module, subject, errMsg, params);
    }

    /**
     * 检查客户端是否已初始化
     */
    private static void checkClient() {
        if (messageClient == null) {
            throw new MessageClientException("MessageClient未初始化，请确保MessageClientAutoConfiguration已加载");
        }
    }
}
