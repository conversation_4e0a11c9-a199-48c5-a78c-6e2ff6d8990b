# Pheidi 消息客户端 Starter

Pheidi消息客户端是一个基于Spring Boot的starter组件，用于简化应用与消息中心的集成，提供消息上报、异常监控、失败重试等功能，支持多种使用方式和丰富的配置选项。

## 功能特性

- **自动配置**：基于Spring Boot自动配置机制，开箱即用
- **多种使用方式**：
  - 直接使用`MessageClient`接口
  - 使用预定义模板的`MessageTemplateClient`
  - 使用静态工具类`MessageSender`
- **异步处理**：所有消息发送基于`CompletableFuture`实现非阻塞异步操作
- **失败重试**：基于Spring Retry实现消息发送失败的自动重试机制
- **监控与日志**：集成Feign客户端监控拦截器，支持请求日志和性能指标收集
- **线程池管理**：独立的消息处理线程池，避免阻塞业务线程，并提供完善的线程池配置

## 快速开始

### 1. 添加依赖

在项目的`pom.xml`中添加以下依赖：

```xml
<dependency>
    <groupId>com.chargebolt</groupId>
    <artifactId>pheidi-client-starter</artifactId>
    <version>0.0.4.release</version>
</dependency>
```

### 2. 配置属性

在`application.yml`或`application.properties`中添加配置：

```yaml
message:
  client:
    app-name: your-app-name           # 应用名称
    app-key: your-app-key             # 应用密钥，用于接口认证
    connect-timeout: 3000             # 连接超时时间（毫秒）
    read-timeout: 5000                # 读取超时时间（毫秒）
    retry-enabled: true               # 是否启用重试
    max-retries: 3                    # 最大重试次数
    retry-interval: 1000              # 重试间隔（毫秒）
    log-enabled: true                 # 是否启用日志
    metric-enabled: false             # 是否启用指标监控
    thread-pool:
      core-pool-size: 5               # 核心线程数
      max-pool-size: 10               # 最大线程数
      queue-capacity: 100             # 队列容量
      thread-name-prefix: message-client-  # 线程名称前缀
```

### 3. 使用方式

#### 方式一：使用 MessageClient

```java
@Service
public class YourService {
    @Autowired
    private MessageClient messageClient;
    
    public void sendNotification(String userId, String content) {
        Map<String, String> params = new HashMap<>();
        params.put("userId", userId);
        params.put("content", content);
        
        messageClient.sendMessage("用户模块", "通知提醒", "用户通知发送", params)
            .thenAccept(result -> {
                if (result) {
                    log.info("消息发送成功");
                } else {
                    log.warn("消息发送失败");
                }
            });
    }
}
```

#### 方式二：使用 MessageTemplateClient

```java
@Service
public class PaymentService {
    @Autowired
    private MessageTemplateClient templateClient;
    
    public void processPayment(String userId, String orderNo, String amount) {
        Map<String, String> params = new HashMap<>();
        params.put("userId", userId);
        params.put("orderNo", orderNo);
        params.put("amount", amount);
        
        templateClient.payReport(userId, "支付成功通知", params)
            .thenAccept(result -> {
                // 处理结果
            });
    }
}
```

#### 方式三：使用 MessageSender 工具类

```java
public class RefundService {
    
    public void processRefund(String userId, String orderNo, String amount) {
        Map<String, String> params = new HashMap<>();
        params.put("userId", userId);
        params.put("orderNo", orderNo);
        params.put("amount", amount);
        
        // 直接使用静态方法发送消息，无需注入
        MessageSender.sendWithRetry("退款模块", userId, "退款处理完成", params)
            .thenAccept(result -> {
                // 处理结果
            });
    }
}
```

#### 方式四：使用对象参数

除了使用Map传递参数外，还可以直接传递对象，客户端会自动提取对象的字段：

```java
public class User {
    private String id;
    private String name;
    private String email;
    
    // getters and setters
}

// 使用
User user = new User();
user.setId("123456");
user.setName("张三");
user.setEmail("<EMAIL>");

messageClient.sendMessage("用户模块", "用户注册", "新用户注册成功", user);
```

## 配置说明

| 配置项                                  | 说明                   | 默认值            |
|---------------------------------------|------------------------|------------------|
| message.client.app-name               | 应用名称                | unknown          |
| message.client.app-key                | 应用Key                | unknown          |
| message.client.connect-timeout        | 连接超时时间（毫秒）      | 3000             |
| message.client.read-timeout           | 读取超时时间（毫秒）      | 5000             |
| message.client.retry-enabled          | 是否启用重试             | true             |
| message.client.max-retries            | 最大重试次数             | 3                |
| message.client.retry-interval         | 重试间隔（毫秒）          | 1000             |
| message.client.log-enabled            | 是否启用日志             | true             |
| message.client.metric-enabled         | 是否启用指标监控          | false            |
| message.client.thread-pool.core-pool-size | 核心线程数          | 5                |
| message.client.thread-pool.max-pool-size  | 最大线程数          | 10               |
| message.client.thread-pool.queue-capacity | 队列容量           | 100              |
| message.client.thread-pool.thread-name-prefix | 线程名称前缀    | message-client-  |

## 架构设计

```mermaid
graph TD
    Application[应用服务] --> MessageSender[消息发送工具类]
    Application --> MessageClient[消息客户端接口]
    Application --> MessageTemplateClient[模板消息客户端]
    
    MessageSender --> DefaultMessageClient[默认消息客户端实现]
    MessageClient --> DefaultMessageClient
    MessageTemplateClient --> MessageClient
    
    DefaultMessageClient --> FeignClientMonitor[Feign客户端监控]
    FeignClientMonitor --> AppMonitorApi[应用监控API]
    AppMonitorApi --> PheidiServer[消息中心服务]
    
    MessageClientProperties[消息客户端配置] --> DefaultMessageClient
    MessageClientAutoConfiguration[自动配置类] --> MessageClient
    
    subgraph 线程池管理
        ThreadPoolConfig[线程池配置]
    end
    ThreadPoolConfig --> DefaultMessageClient
```

## 扩展开发

### 自定义消息模板

可以通过扩展`MessageTemplateClient`类添加自定义的业务模板：

```java
@Service
public class CustomMessageTemplateClient extends MessageTemplateClient {
    
    /**
     * 发送用户注册验证码
     */
    public CompletableFuture<Boolean> sendVerificationCode(String mobile, String code) {
        Map<String, String> params = new HashMap<>();
        params.put("mobile", mobile);
        params.put("code", code);
        params.put("expireMinutes", "10");
        
        return messageClient.sendMessageWithRetry("验证码模块", mobile, "注册验证码", params);
    }
}
```

### 自定义消息客户端实现

如果需要自定义消息发送逻辑，可以实现`MessageClient`接口：

```java
@Service
public class CustomMessageClient implements MessageClient {
    
    @Override
    public CompletableFuture<Boolean> sendMessage(String module, String subject, String errMsg, Map<String, String> params) {
        // 自定义实现逻辑
    }
    
    @Override
    public CompletableFuture<Boolean> sendMessageWithRetry(String module, String subject, String errMsg, Map<String, String> params) {
        // 自定义实现逻辑
    }
}
```

## 注意事项

1. 所有消息发送方法都是异步的，返回`CompletableFuture<Boolean>`
2. 带有`WithRetry`后缀的方法会在失败时自动重试
3. 为避免阻塞业务线程，消息处理使用独立的线程池
4. 配置属性支持在运行时通过Spring Cloud Config动态刷新
5. 当`log-enabled`为`true`时，会记录详细的请求日志
6. 当`metric-enabled`为`true`时，会收集请求性能指标
