.git
*.class

# Log file #
*.log

# Package Files #
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

######################
# 解决maven产生的文件
######################

target/
**/target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

######################
# 解决各类编辑器自动产生的文件
######################
*.iml
*/*.iml
## Directory-based project format:
.idea/
# IntelliJ
/out/
/target/

# mpeltonen/sbt-idea plugin
.idea_modules/

# Myeclipse #
.classpath
.project
.DS_Store
*/.DS_Store

/data/
.run/
.env
.vscode
.windsurfrules
