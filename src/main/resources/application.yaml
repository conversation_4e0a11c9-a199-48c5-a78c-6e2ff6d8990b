hystrix:
  command:
    default.execution.isolation.thread.timeoutInMilliseconds: 5000
    getAccessToken.execution.isolation.thread.timeoutInMilliseconds: 5000
info:
  component: '@project.description@'
  name: '@project.name@'
  os: ${os.name}
  version: '@project.version@'
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}/${spring.application.name}.log
server:
  port: 8080
  tomcat:
    mbeanregistry:
      enabled: true
    accesslog:
      buffered: true
      enabled: true
      directory: ${user.home}/logs/${spring.application.name}/
      file-date-format: .yyyyMMdd
      pattern: "%a||%t||%{Host}i||%p||%m||%U||%H||%q||%s||%b||%{Referer}i||%{User-Agent}i||%{X-Real-Ip}i||%{X-Forwarded-For}i||%D"
      prefix: tomcat_access_log
      rename-on-rotate: false
      request-attributes-enabled: false
      rotate: true
      suffix: .log
spring:
  output:
    ansi:
      enabled: always
  servlet:
    multipart:
      max-file-size: **********
      max-request-size: **********
      enabled: true
      location: '/tmp'

management:
  server:
    port: 8888
  health:
    db:
      enabled: false
  endpoint:
    health:
      show-details: always


