spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  datasource:
    rds:
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 5
      max-active: 20
      min-idle: 5
      url: ************************************************************************************
      username: chargebolt
      password: 5X0nPTEAQWSkRbJ
    druid:
      filter:
        config:
          enabled: false
      stat-view-servlet:
        enabled: false
      web-stat-filter:
        enabled: false
  redis:
    host: redis-id01.gocas.id
    password: Kongge789
    pool:
      max-active: 30
      max-idle: 5
      max-wait: 3000
    port: 6379
    timeout: 3000

swagger:
  enable: false
springdoc:
  packages-to-scan: com.chargebolt.pheidi.controller
  api-docs:
    enable: false
  swagger-ui:
    tryItOutEnabled: true
    path: /api-documents
    enable: false

# GoCasForce
onesignal:
  appid: ************************************
  apikey: os_v2_app_p4yyaxhp5bghtocaf3uuzn7lqyhsg4zkuyqe6mmat7vkemcayhk7wkdnj6ihxdjgv37znst6j32zvo3dphuk4tbwvglkbynaix333vy


pheidi:
  profile: 'id01'
  remote:
    url:
      apollo: 'adela:8080'
      hera: 'chargebolt-hera:8080'
      prometheus:
        onesignal: 'https://api.onesignal.com'
      demeter: 'chargebolt-ezreal:8080'
      talos: 'chargebolt-ezreal:8080'

message:
  redirectUrl: 'https://papillon-id01.gocas.id/message'

rateLimiter:
  # 间隔时间
  rate-interval: 5
  logPush:
    # 速率
    rate: 5
  message:
    # 速率
    rate: 50