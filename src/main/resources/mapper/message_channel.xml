<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.pheidi.dao.MessageChannelDao">
    <resultMap id="BaseResultMap" type="com.chargebolt.pheidi.domain.MessageChannel">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="message_id" jdbcType="BIGINT" property="messageId"/>
        <result column="channel" jdbcType="INTEGER" property="channel"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="retry_count" jdbcType="INTEGER" property="retryCount"/>
        <result column="last_attempt_time" jdbcType="TIMESTAMP" property="lastAttemptTime"/>
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg"/>
        <result column="target_type" jdbcType="TINYINT" property="targetType"/>
        <result column="target_value" jdbcType="VARCHAR" property="targetValue"/>
        <result column="scheduled_time" jdbcType="TIMESTAMP" property="scheduledTime"/>
        <result column="completed_time" jdbcType="TIMESTAMP" property="completedTime"/>
        <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, message_id, channel, status, retry_count, last_attempt_time, error_msg, target_type,
        target_value, scheduled_time, completed_time, gmt_create, gmt_update, deleted
    </sql>

    <insert id="insertMessageChannel" parameterType="com.chargebolt.pheidi.domain.MessageChannel" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO message_channel (
            message_id,
            channel,
            status,
            retry_count,
            last_attempt_time,
            error_msg,
            target_type,
            target_value,
            scheduled_time,
            completed_time,
            gmt_create,
            gmt_update,
            deleted
        ) VALUES (
            #{messageId},
            #{channel},
            #{status},
            #{retryCount},
            #{lastAttemptTime},
            #{errorMsg},
            #{targetType},
            #{targetValue},
            #{scheduledTime},
            #{completedTime},
            #{gmtCreate},
            #{gmtUpdate},
            #{deleted}
        )
    </insert>

    <select id="getMessageChannelById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM message_channel
        WHERE id = #{id} AND deleted = 0
    </select>

    <update id="updateMessageChannelStatus">
        UPDATE message_channel
        SET status = #{status},
            gmt_update = #{gmtUpdate}
        WHERE id = #{id}
    </update>

    <update id="updateMessageChannel" parameterType="com.chargebolt.pheidi.domain.MessageChannel">
        UPDATE message_channel
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="retryCount != null">
                retry_count = #{retryCount},
            </if>
            <if test="lastAttemptTime != null">
                last_attempt_time = #{lastAttemptTime},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg},
            </if>
            <if test="scheduledTime != null">
                scheduled_time = #{scheduledTime},
            </if>
            <if test="completedTime != null">
                completed_time = #{completedTime},
            </if>
            <if test="gmtUpdate != null">
                gmt_update = #{gmtUpdate},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- queryPendingChannels --> 

    <select id="queryPendingChannels"  resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM message_channel
        WHERE `status` in (0,2)
        AND retry_count &lt;= #{maxRetryCount}
        AND deleted = 0
        ORDER BY gmt_create ASC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- updateRetryCount --> 

    <update id="updateRetryCount">
        UPDATE message_channel
        SET retry_count = retry_count + 1,
            last_attempt_time = #{lastAttemptTime},
            error_msg = #{errMsg},
            gmt_update = #{gmtUpdate}
        WHERE id = #{id}
    </update>

    <!-- selectByMessageIdAndChannel --> 

    <select id="selectByMessageIdAndChannel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM message_channel
        WHERE message_id = #{msgId}
        AND `channel` = #{channel}
        AND deleted = 0
    </select>
</mapper>
