<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.pheidi.dao.MessageDao">
    <resultMap id="BaseResultMap" type="com.chargebolt.pheidi.domain.Message">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="biz_type" jdbcType="TINYINT" property="bizType"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="priority" jdbcType="TINYINT" property="priority"/>
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="redirect_url" jdbcType="VARCHAR" property="redirectUrl"/>
        <result column="redirect_goal" jdbcType="TINYINT" property="redirectGoal"/>
        <result column="generate_time" jdbcType="TIMESTAMP" property="generateTime"/>
        <result column="target_type" jdbcType="TINYINT" property="targetType"/>
        <result column="target_value" jdbcType="VARCHAR" property="targetValue"/>
        <result column="template_id" jdbcType="BIGINT" property="templateId"/>
        <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id , biz_type, status, priority, expire_time, title, content, redirect_url, redirect_goal, generate_time, target_type, target_value, template_id,
        gmt_create, gmt_update, deleted
    </sql>

    <insert id="insertMessage" keyProperty="id" useGeneratedKeys="true">
        insert into message
        (biz_type, status, priority, expire_time, title, content, redirect_url, redirect_goal, generate_time, target_type, target_value, template_id, gmt_create,
        gmt_update, deleted)
        values (#{bizType}, #{status}, #{priority}, #{expireTime}, #{title}, #{content}, #{redirectUrl}, #{redirectGoal}, #{generateTime}, #{targetType},
        #{targetValue}, #{templateId}, #{gmtCreate},
        #{gmtUpdate}, #{deleted})
    </insert>

    <insert id="batchInsert">
        INSERT INTO message (
            biz_type,
            status,
            priority,
            expire_time,
            title,
            content,
            redirect_url,
            redirect_goal,
            generate_time,
            target_type,
            target_value,
            template_id,
            gmt_create,
            gmt_update,
            deleted
        ) VALUES
        <foreach collection="messages" item="item" separator=",">
            (
                #{item.bizType},
                #{item.status},
                #{item.priority},
                #{item.expireTime},
                #{item.title},
                #{item.content},
                #{item.redirectUrl},
                #{item.redirectGoal},
                #{item.generateTime},
                #{item.targetType},
                #{item.targetValue},
                #{item.templateId},
                #{item.gmtCreate},
                #{item.gmtUpdate},
                #{item.deleted}
            )
        </foreach>
    </insert>

    <update id="updateMessageStatus">
        update message
        set
        status = #{status},
        gmt_update = UNIX_TIMESTAMP(NOW(3)) * 1000
        where id = #{id}
    </update>


    <!-- getById -->

    <select id="getById" resultType="com.chargebolt.pheidi.domain.Message">
        select
        <include refid="Base_Column_List"/>
        from message
        where id = #{messageId}
        and deleted = 0
    </select>

    <!-- getByIds -->

    <select id="getByIds" resultType="com.chargebolt.pheidi.domain.Message">
        select
        <include refid="Base_Column_List"/>
        from message
        where id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted = 0
        order by id desc
    </select>
    
    <!-- 获取指定ID及更大ID的未推送消息 -->
    <select id="getUnpushedMessagesFromId" resultType="com.chargebolt.pheidi.domain.Message">
        select
        <include refid="Base_Column_List"/>
        from message
        where id <![CDATA[>=]]> #{messageId}
        and status = #{status}
        and deleted = 0
        order by id asc
    </select>
</mapper>