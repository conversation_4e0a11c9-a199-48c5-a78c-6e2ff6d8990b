<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.pheidi.dao.MessageTemplateDao">
    <resultMap id="BaseResultMap" type="com.chargebolt.pheidi.domain.MessageTemplate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="biz_type" jdbcType="TINYINT" property="bizType"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="channels" jdbcType="VARCHAR" property="channels"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="redirect_url" jdbcType="VARCHAR" property="redirectUrl"/>
        <result column="redirect_goal" jdbcType="TINYINT" property="redirectGoal"/>
        <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id , `name`,`code`, biz_type, `status`, channels, title, content, redirect_url, redirect_goal, gmt_create, gmt_update, deleted
    </sql>

    <insert id="insertMessageTemplate" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO message_template (
            `code`,
            biz_type,
            `status`,
            channels,
            title,
            `name`,
            content,
            redirect_url,
            redirect_goal,
            gmt_create,
            gmt_update,
            deleted
        ) VALUES (
            #{code},
            #{bizType},
            #{status},
            #{channels},
            #{title},
            #{name},
            #{content},
            #{redirectUrl},
            #{redirectGoal},
            #{gmtCreate},
            #{gmtUpdate},
            #{deleted}
        )
    </insert>

    <update id="updateMessageTemplate">
        update message_template
        <set>
            <if test="name != null">
                `name` = #{name},
            </if>
            <if test="bizType != null">
                biz_type = #{bizType},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="channels != null">
                channels = #{channels},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="redirectUrl != null">
                redirect_url = #{redirectUrl},
            </if>
            <if test="redirectGoal != null">
                redirect_goal = #{redirectGoal},
            </if>
            gmt_update = UNIX_TIMESTAMP(NOW(3)) * 1000
        </set>
        where id = #{id}
    </update>

    <select id="getMessageTemplateByCode" resultType="com.chargebolt.pheidi.domain.MessageTemplate">
        SELECT
        <include refid="Base_Column_List"/>
        FROM message_template
        WHERE code = #{code}
        LIMIT 1
    </select>

    <select id="getMessageTemplateByName" resultType="com.chargebolt.pheidi.domain.MessageTemplate">
        SELECT
        <include refid="Base_Column_List"/>
        FROM message_template
        WHERE name = #{name}
        LIMIT 1
    </select>

    <select id="getMessageTemplateById" resultType="com.chargebolt.pheidi.domain.MessageTemplate">
        SELECT
        <include refid="Base_Column_List"/>
        FROM message_template
        WHERE id = #{id}
        LIMIT 1
    </select>

    <!-- queryMessageTemplateList -->

    <select id="queryMessageTemplateList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM message_template
        <where>
            <if test="templateName != null and templateName != ''">
                AND `name` LIKE CONCAT('%', #{templateName}, '%')
            </if>
            <if test="templateCode != null">
                AND `code` = #{templateCode}
            </if>
            <if test="bizType != null">
                AND biz_type = #{bizType}
            </if>
            <if test="status != null">
                AND `status` = #{status}
            </if>
        </where>
        ORDER BY id DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="countMessageTemplate" resultType="int">
        SELECT COUNT(*)
        FROM message_template
        <where>
            <if test="templateName != null and templateName != ''">
                AND name LIKE CONCAT('%', #{templateName}, '%')
            </if>
            <if test="templateCode != null">
                AND code = #{templateCode}
            </if>
            <if test="bizType != null">
                AND biz_type = #{bizType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <!-- updateMessageTemplateStatus --> 

    <update id="updateMessageTemplateStatus">
        UPDATE message_template
        SET `status` = #{status}
        WHERE id = #{templateId}
    </update>

    <!-- queryAllTemplates --> 

    <select id="queryAllTemplates" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM message_template
        WHERE deleted = 0
        ORDER BY id DESC
        limit 500
    </select>
</mapper>