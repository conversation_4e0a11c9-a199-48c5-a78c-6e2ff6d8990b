<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chargebolt.pheidi.dao.MessageReadStatusDao">
    <resultMap id="BaseResultMap" type="com.chargebolt.pheidi.domain.MessageReadStatus">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="message_id" jdbcType="BIGINT" property="messageId"/>
        <result column="biz_type" jdbcType="TINYINT" property="bizType"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="read_time" jdbcType="TIMESTAMP" property="readTime"/>
        <result column="channel" jdbcType="INTEGER" property="channel"/>
        <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, message_id,biz_type, user_id, status, read_time, channel, gmt_create, gmt_update, deleted
    </sql>

    <insert id="insert" parameterType="com.chargebolt.pheidi.domain.MessageReadStatus" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO message_read_status (
            message_id,
            biz_type,
            user_id,
            `status`,
            read_time,
            `channel`,
            gmt_create,
            gmt_update,
            deleted
        ) VALUES (
            #{messageId},
            #{bizType},
            #{userId},
            #{status},
            #{readTime},
            #{channel},
            #{gmtCreate},
            #{gmtUpdate},
            #{deleted}
        )
    </insert>

    <update id="updateMessageReadStatus">
        UPDATE message_read_status
        SET `status` = #{status},
            read_time = #{readTime},
            gmt_update = #{gmtUpdate}
        WHERE id = #{id}
    </update>

    <select id="countUnreadByBizType" resultType="java.util.Map">
        select biz_type, count(1) as unread_count
        from message_read_status
        where user_id = #{externalId}
        and deleted = 0
        and `status` = 0
        group by biz_type
    </select>

    <select id="queryLatestMessageByBizType" resultMap="BaseResultMap">
        SELECT *
        FROM message_read_status
        WHERE deleted = 0 and biz_type = #{bizType} and user_id = #{externalId}
        ORDER BY gmt_create DESC
        limit 1
    </select>

    <select id="queryMessageList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from message_read_status
        where user_id = #{externalId}
        <if test="bizType != null">
            and biz_type = #{bizType}
        </if>
        and deleted = 0
        order by gmt_create desc
        limit #{offset}, #{pageSize}
    </select>

    <select id="countMessage" resultType="java.lang.Integer">
        select count(1)
        from message_read_status
        where user_id = #{externalId}
        <if test="bizType != null">
            and biz_type = #{bizType}
        </if>
        and deleted = 0
    </select>

    <select id="unreadNum" resultType="java.lang.Integer">
        select count(1)
        from message_read_status
        where user_id = #{externalId}
        and deleted = 0
        and `status` = 0
    </select>

    <update id="batchUpdateStatus">
        update message_read_status
        set `status` = #{status},
            gmt_update = UNIX_TIMESTAMP(NOW(3)) * 1000
        where user_id = #{targetId}
        <if test="bizType != null">
            and biz_type = #{bizType}
        </if>
        and deleted = 0
        and `status` = 0
    </update>

    <!-- countTotalByBizType -->

    <select id="countTotalByBizType" resultType="java.util.Map">
        SELECT biz_type, COUNT(1) AS total_count
        FROM message_read_status
        WHERE deleted = 0 and user_id = #{externalId}
        GROUP BY biz_type
    </select>
</mapper>
