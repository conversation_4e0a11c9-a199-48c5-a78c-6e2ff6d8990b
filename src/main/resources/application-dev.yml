spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  datasource:
    rds:
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 5
      max-active: 20
      min-idle: 5
      url: ******************************************************************************
      username: chargebolt
      password: 5X0nPTEAQWSkRbJ
    druid:
      filter:
        config:
          enabled: false
      stat-view-servlet:
        enabled: false
      web-stat-filter:
        enabled: false
  redis:
    host: oversea-dev.redis.diancs.com
    password: 4dd7650E368672ad
    pool:
      max-active: 30
      max-idle: 5
      max-wait: 3000
    port: 6379
    timeout: 3000

swagger:
  enable: true
springdoc:
  packages-to-scan: com.chargebolt.pheidi.controller
  api-docs:
    enable: true
  swagger-ui:
    tryItOutEnabled: true
    path: /api-documents
    enable: true

# OneSignal 电小二测试
onesignal:
  appid: ************************************
  apikey: os_v2_app_eyob5nnn7rbnxblrlno22n4iqpj4xyvqwaruaau744zvuwaehzr3haimxcklcefqeio2vwsg2sla55dk3advszohr4rsikfa2vgztly


pheidi:
  profile: 'dev'
  redisKeyPrefix: 'chargebolt'
  alter: 'alter-1072768272912355328'
  remote:
    url:
      apollo: 'adela:8080'
      hera: 'chargebolt-hera:8080'
      prometheus:
        onesignal: 'https://api.onesignal.com'
      demeter: 'chargebolt-ezreal:8080'
      talos: 'chargebolt-ezreal:8080'

message:
  redirectUrl: 'https://papillon-alter-1072768272912355328.six.dian-dev.com/message'

rateLimiter:
  # 间隔时间
  rate-interval: 5
  logPush:
    # 速率
    rate: 5
  message:
    # 速率
    rate: 5
