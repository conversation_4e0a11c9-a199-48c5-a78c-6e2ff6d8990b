spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  datasource:
    rds:
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 5
      max-active: 20
      min-idle: 5
      url: ******************************************************************************************
      user-name: chargebolt
      password: 5X0nPTEAQWSkRbJ
    druid:
      filter:
        config:
          enabled: false
      stat-view-servlet:
        enabled: false
      web-stat-filter:
        enabled: false
  redis:
    host: redis-vn01.chargebolt.com
    password: Kongge789
    pool:
      max-active: 30
      max-idle: 5
      max-wait: 3000
    port: 6379
    timeout: 3000

swagger:
  enable: false
springdoc:
  packages-to-scan: com.chargebolt.pheidi.controller
  api-docs:
    enable: false
  swagger-ui:
    tryItOutEnabled: true
    path: /api-documents
    enable: false

# vn01 与 sg 线上的 配置共用
onesignal:
  appid: ************************************
  apikey: os_v2_app_6f3hy4idgrarno3lfw4nylyn55nxsig4vmwu4g5yn7spjmgn6bwawcejr5cclkxa3gahmp26tu7zbrxejjml7xoauts4yrjb6jliohi


pheidi:
  profile: 'vn01'
  remote:
    url:
      apollo: 'adela:8080'
      hera: 'chargebolt-hera:8080'
      prometheus:
        onesignal: 'https://api.onesignal.com'
      demeter: 'chargebolt-ezreal:8080'
      talos: 'chargebolt-ezreal:8080'

message:
  redirectUrl: 'https://papillon-vn01.chargebolt.com/message'

rateLimiter:
  # 间隔时间
  rate-interval: 5
  logPush:
    # 速率
    rate: 5
  message:
    # 速率
    rate: 50