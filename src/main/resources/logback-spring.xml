<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="60 seconds">
    <!--引入默认配置-->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <springProperty defaultValue="INFO" name="bizLevel" scope="context" source="logging.level.biz"/>
    <springProperty defaultValue="INFO" name="springLevel" scope="context" source="logging.level.spring"/>

    <!--自定义环境变量-->
    <property name="LOG_PATH" value="${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}"/>
    <property name="LOG_FILE" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/spring.log}"/>
    <property name="ERROR_FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS}|{%m} %n"/>

    <!-- 项目全量日志，建议每个logger中都加入此appender，用于链路调用跟踪 -->
    <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="SPRING_FILE">
        <file>${LOG_FILE}</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志命名:单个文件大于128MB 按照时间+自增i 生成log文件 -->
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>${MAX_HISTORY:-7}</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <!-- 日志格式 -->
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 错误日志，只打印ERROR级别未处理异常堆栈跟踪日志：用于报警通知-->
    <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="ERROR_FILE">
        <file>${LOG_PATH}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/error.log.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>${MAX_HISTORY:-7}</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <encoder>
            <pattern>${ERROR_FILE_LOG_PATTERN}</pattern>
        </encoder>
        <!-- 日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 其他自定义业务划分日志 -->
    <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="BIZ_FILE">
        <file>${LOG_PATH}/biz.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/biz.log.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>${MAX_HISTORY:-7}</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- RT监控日志，只打印INFO级别@RTMonitor监控日志：用于延时分析报警通知-->
    <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="RT_FILE">
        <file>${LOG_PATH}/rt.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/rt.log.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>${MAX_HISTORY:-7}</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <encoder>
            <pattern>${ERROR_FILE_LOG_PATTERN}</pattern>
        </encoder>
        <!-- 日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>INFO</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--日志topic为error，即 @Slf4j(topic = "error") 或 LoggerFactory.getLogger("error") 方式打印的日志-->
    <logger additivity="false" level="ERROR" name="error">
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="SPRING_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!--日志topic为biz，即 @Slf4j(topic = "biz") 或 LoggerFactory.getLogger("biz") 方式打印的日志-->
    <logger additivity="false" level="${bizLevel}" name="biz">
        <appender-ref ref="BIZ_FILE"/>
        <appender-ref ref="SPRING_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!--日志topic为rt，即 @Slf4j(topic = "rt") 或 LoggerFactory.getLogger("rt") 方式打印的日志-->
    <logger additivity="false" level="INFO" name="rt">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="RT_FILE"/>
    </logger>

    <!--默认日志topic，即 @Slf4j 或 LoggerFactory.getLogger(ClassName.class}) 方式打印的日志-->
    <root level="${springLevel}">
        <appender-ref ref="SPRING_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </root>

    <!--JMX管理 Spring Boot Admin-->
    <jmxConfigurator/>
</configuration>