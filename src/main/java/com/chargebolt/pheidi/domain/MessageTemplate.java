package com.chargebolt.pheidi.domain;

import lombok.Data;

@Data
public class MessageTemplate {

    private Long id;
    /**
     * 模板名称
     */
    private String name;
    /**
     * 模板 code
     */
    private String code;
    /**
     * 业务类型
     */
    private Integer bizType;
    /**
     * 启用状态
     */
    private Integer status;
    /**
     * 推送渠道
     */
    private String channels;
    /**
     * 消息标题
     */
    private String title;
    /**
     * 消息内容模板
     */
    private String content;
    /**
     * 跳转链接
     */
    private String redirectUrl;
    /**
     * 跳转目标
     */
    private Integer redirectGoal;

    private Long gmtCreate;
    private Long gmtUpdate;
    private Integer deleted;
}
