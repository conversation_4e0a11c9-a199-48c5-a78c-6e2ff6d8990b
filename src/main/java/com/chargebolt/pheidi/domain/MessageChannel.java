package com.chargebolt.pheidi.domain;

import java.time.LocalDateTime;
import lombok.Data;

@Data
public class MessageChannel {
    /**
     * 主键
     */
    private Long id;

    /**
     * 消息ID
     */
    private Long messageId;

    /**
     * 推送渠道
     */
    private Integer channel;

    /**
     * 消息推送状态，0 待推送 1 已推送 2 推送失败 3 丢弃
     */
    private Integer status;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最后尝试时间
     */
    private LocalDateTime lastAttemptTime;

    /**
     * 最后错误信息
     */
    private String errorMsg;

    /**
     * 推送对象类型
     */
    private Integer targetType;

    /**
     * 推送对象id
     */
    private String targetValue;

    /**
     * 计划下次发送时间
     */
    private LocalDateTime scheduledTime;

    /**
     * 完成时间
     */
    private LocalDateTime completedTime;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;

    /**
     * 是否删除
     */
    private Integer deleted;
}
