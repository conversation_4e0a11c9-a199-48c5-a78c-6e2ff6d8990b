package com.chargebolt.pheidi.domain;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class Message {

    private Long id;
    /**
     * 业务类型
     */
    private Integer bizType;
    /**
     * 消息状态
     */
    private Integer status;
    /**
     * 优先级程度，数值越小优先级越高，默认为 0
     */
    private Integer priority;
    /**
     * 消息过期时间
     */
    private LocalDateTime expireTime;
    /**
     * 消息标题
     */
    private String title;
    /**
     * 消息内容模板
     */
    private String content;
    /**
     * 跳转链接
     */
    private String redirectUrl;
    /**
     * 跳转目标
     */
    private Integer redirectGoal;

    /**
     * 生成时间
     */
    private LocalDateTime generateTime;

    /**
     * 模板 ID
     */
    private Long templateId;

    /**
     * 目标类型
     */
    private Integer targetType;

    /**
     * 目标值
     */
    private String targetValue;

    private Long gmtCreate;
    private Long gmtUpdate;
    private Integer deleted;
}
