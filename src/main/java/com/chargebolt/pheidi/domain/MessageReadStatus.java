package com.chargebolt.pheidi.domain;

import java.time.LocalDateTime;

import lombok.Data;

@Data
public class MessageReadStatus {
    /**
     * 主键
     */
    private Long id;
    /**
     * 消息ID
     */
    private Long messageId;
    /**
     * 业务类型
     */
    private Integer bizType;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 已读状态
     */
    private Integer status;
    /**
     * 已读时间
     */
    private LocalDateTime readTime;
    /**
     * 推送渠道
     */
    private Integer channel;
    /**
     * 创建时间
     */
    private Long gmtCreate;
    /**
     * 更新时间
     */
    private Long gmtUpdate;
    /**
     * 是否删除
     */
    private Integer deleted;
}
