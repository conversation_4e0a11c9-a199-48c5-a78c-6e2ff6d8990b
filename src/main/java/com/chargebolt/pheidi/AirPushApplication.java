package com.chargebolt.pheidi;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@SpringBootApplication
@EnableScheduling
@EnableFeignClients(basePackages = {"com.chargebolt.pheidi.remote"})
public class AirPushApplication {

    public static void main(String[] args) {
        SpringApplication.run(AirPushApplication.class, args);
    }

}
