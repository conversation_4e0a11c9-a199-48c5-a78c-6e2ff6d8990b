package com.chargebolt.pheidi.controller;

import lombok.extern.slf4j.Slf4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.chargebolt.pheidi.common.MessageStatusEnum;
import com.chargebolt.pheidi.dao.MessageDao;
import com.chargebolt.pheidi.dao.MessageTemplateDao;
import com.chargebolt.pheidi.domain.Message;
import com.chargebolt.pheidi.domain.MessageTemplate;
import com.chargebolt.pheidi.service.MessagePushService;

@Slf4j
@Tag(name = "调试")
@RestController
public class DebugController {

    @Resource
    private MessageDao messageDao;

    @Resource
    private MessageTemplateDao templateDao;

    @Resource
    private MessagePushService messagePushService;

    /**
     * 
     * 根据指定的消息ID，查询该ID及大于该ID的所有未推送消息（状态为0），
     * 然后对每条消息获取其对应的模板，并调用消息推送服务进行异步推送。
     * 推送过程中会在推送策略内部自动更新消息状态为已推送（状态为1）。
     * 
     * 调用链路：
     * 1. 查询未推送消息 - MessageDao.getUnpushedMessagesFromId
     * 2. 获取消息模板 - MessageTemplateDao.getMessageTemplateById
     * 3. 异步推送消息 - MessagePushService.pushMessage
     * 
     * @param messageId 消息ID，查询该消息ID及大于该ID的未推送消息
     * @return 推送结果统计信息，包含总消息数、成功数和失败数
     */
    @Operation(summary = "手动推送消息", description = "根据消息ID查询该消息及大于该ID的未推送消息，并进行推送")
    @GetMapping("/manual-push")
    public String manualPush(
            @Parameter(description = "消息ID", required = true) @RequestParam("messageId") Long messageId) {
        log.info("开始手动推送消息，起始消息ID: {}", messageId);

        // 查询指定ID及更大ID的未推送消息
        List<Message> unpushedMessages = messageDao.getUnpushedMessagesFromId(messageId,
                MessageStatusEnum.WAIT_PUSH.getStatus());

        if (unpushedMessages.isEmpty()) {
            return "没有找到需要推送的消息";
        }

        int successCount = 0;
        int failCount = 0;

        for (Message message : unpushedMessages) {
            try {
                // 获取消息对应的模板
                MessageTemplate template = templateDao.getMessageTemplateById(message.getTemplateId());
                if (template == null) {
                    log.error("消息模板不存在，messageId: {}, templateId: {}", message.getId(), message.getTemplateId());
                    failCount++;
                    continue;
                }

                // 推送消息
                messagePushService.pushMessage(message, template.getChannels());
                successCount++;
            } catch (Exception e) {
                failCount++;
                log.error("推送消息失败，messageId: {}, error: {}", message.getId(), e.getMessage(), e);
            }
        }

        return String.format("推送完成，总消息数: %d, 成功: %d, 失败: %d", unpushedMessages.size(), successCount, failCount);
    }
}
