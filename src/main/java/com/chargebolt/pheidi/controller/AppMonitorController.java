package com.chargebolt.pheidi.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.chargebolt.pheidi.dto.LogMsgDTO;
import com.chargebolt.pheidi.dto.PheidiResponse;
import com.chargebolt.pheidi.remote.DingTalkRobotApi;
import com.chargebolt.pheidi.util.RateLimiterUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/monitor")
public class AppMonitorController {

    @Resource
    private DingTalkRobotApi dingTalkRobotApi;

    @Resource
    private RateLimiterUtils rateLimiterUtils;

    @Resource
    private RedissonClient redissonClient;

    @Value("${rateLimiter.logPush.rate}")
    private int logPushRate;

    // 告警消息间隔时间（毫秒）
    private static final long ALERT_INTERVAL_MS = 5 * 60 * 1000; // 5分钟

    // 告警时间戳Redis前缀
    private static final String ALERT_TIMESTAMP_PREFIX = "log_push_alert_timestamp:";

    // 请求计数Redis前缀
    private static final String REQUEST_COUNT_PREFIX = "log_push_count:";

    private static final Map<String, String> appKeyMap = new HashMap<String,String>(){{
        put("hera-1745835188842", "hera");
        put("kronos-1745835213841", "kronos");
        put("ezreal-1745835226842", "ezreal");
    }};

    /**
     * 推送日志
     * 添加限流功能，每个应用每分钟最多允许60次请求（平均每秒1次）
     * 
     * @param dto 日志消息
     * @return 处理结果
     */
    @PostMapping("/log/push")
    public PheidiResponse logPush(@RequestBody LogMsgDTO dto, HttpServletRequest request) {
        // 简单验证调用方身份
        String authVal = request.getHeader("Authorization");
        if (authVal == null || appKeyMap.get(authVal) == null || !appKeyMap.get(authVal).equals(dto.getAppName())) {
            return PheidiResponse.error("auth failed");
        }

        // 增加请求计数
        incrementRequestCount(dto.getAppName());

        // 限流处理，每分钟最多60次请求
        String rateLimiterKey = "log:push:" + dto.getAppName();
        boolean acquired = rateLimiterUtils.tryAcquire(rateLimiterKey, logPushRate, TimeUnit.MINUTES);
        if (!acquired) {
            log.warn("日志推送频率超限，应用={}", dto.getAppName());
            // 检查是否需要发送告警（距离上次告警是否超过5分钟）
            sendRateLimitAlertIfNeeded(dto.getAppName());
            return PheidiResponse.error("日志推送过于频繁，请稍后再试");
        }

        // 直接通过钉钉推送日志
        try {
            dingTalkRobotApi.send(dto);
        } catch (Exception e) {
            log.error("logPush error, param={}", JSON.toJSONString(dto), e);
            return PheidiResponse.error(e.getMessage());
        }
        return PheidiResponse.success();
    }

    /**
     * 检查是否需要发送限流告警
     * 如果距离上次告警超过5分钟，则发送告警并更新时间戳
     * 使用Redis存储最后一次告警的时间戳，保证在分布式环境下的一致性
     * 
     * @param appName 应用名称
     */
    private void sendRateLimitAlertIfNeeded(String appName) {
        String alertKey = ALERT_TIMESTAMP_PREFIX + appName;
        RBucket<Long> lastAlertTimeBucket = redissonClient.getBucket(alertKey);
        long currentTime = System.currentTimeMillis();

        // 获取上次告警时间，如果不存在则设置为0
        long lastAlertTime = lastAlertTimeBucket.isExists() ? lastAlertTimeBucket.get() : 0;

        // 如果距离上次告警超过5分钟或者是首次告警
        if (currentTime - lastAlertTime > ALERT_INTERVAL_MS) {
            try {
                // 构建告警消息
                LogMsgDTO alertMsg = new LogMsgDTO();
                alertMsg.setAppName("pheidi");
                alertMsg.setModule("monitor");
                alertMsg.setSubject("限流告警");
                alertMsg.setLogMsg("【限流告警】应用 " + appName + " 日志推送频率超限，请检查客户端是否存在异常");
                alertMsg.setLogTime(String.valueOf(currentTime));
                // 发送告警消息
                dingTalkRobotApi.send(alertMsg);

                log.info("发送限流告警成功，应用={}", appName);
            } catch (Exception e) {
                log.error("发送限流告警失败，应用={}", appName, e);
            } finally {
                // 更新最后一次告警时间
                lastAlertTimeBucket.set(currentTime);
            }
        } else {
            // 距离上次告警未超过5分钟，不发送告警
            log.debug("限流告警频率控制，距离上次告警时间{}ms，应用={}",
                    (currentTime - lastAlertTime), appName);
        }
    }

    /**
     * 增加请求计数
     * 使用Redis存储每个应用的请求次数，保证在分布式环境下的一致性
     * 
     * @param appName 应用名称
     */
    private void incrementRequestCount(String appName) {
        try {
            String countKey = REQUEST_COUNT_PREFIX + appName;
            redissonClient.getAtomicLong(countKey).incrementAndGet();
        } catch (Exception e) {
            log.error("增加请求计数失败，应用={}", appName, e);
        }
    }
}
