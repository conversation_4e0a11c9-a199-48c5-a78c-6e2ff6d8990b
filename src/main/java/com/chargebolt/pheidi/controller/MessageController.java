package com.chargebolt.pheidi.controller;


import java.util.Objects;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.chargebolt.pheidi.dto.LogMsgDTO;
import com.chargebolt.pheidi.remote.DingTalkRobotApi;
import com.chargebolt.pheidi.request.MarkReadRequest;
import com.chargebolt.pheidi.request.MessageBody;
import com.chargebolt.pheidi.request.MessageQuery;
import com.chargebolt.pheidi.request.UnReadMessageCountQuery;
import com.chargebolt.pheidi.response.MessageGroupVo;
import com.chargebolt.pheidi.response.MessageListItemVo;
import com.chargebolt.pheidi.response.PageData;
import com.chargebolt.pheidi.service.MessageService;
import com.chargebolt.pheidi.util.RateLimiterUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import so.dian.mofa3.lang.domain.Result;
import so.dian.mofa3.lang.exception.CheckParamException;
import so.dian.mofa3.template.controller.ControllerCallback;

@Slf4j
@Tag(name = "消息中心")
@RestController
public class MessageController extends BaseController {

    @Resource
    private MessageService messageService;
    @Resource
    private DingTalkRobotApi dingTalkRobotApi;
    @Resource
    private RateLimiterUtils rateLimiterUtils;
    @Value("${rateLimiter.message.rate}")
    private long messageRate;
    @Resource
    private RedissonClient redissonClient;
    // 告警消息间隔时间（毫秒）
    private static final long ALERT_INTERVAL_MS = 5 * 60 * 1000; // 5分钟

    /**
     * 发送消息
     */
    @SuppressWarnings("unchecked")
    @Operation(summary = "发送消息")
    @PostMapping("/message-center/sendMessage")
    public Result<Boolean> sendMessage(@RequestBody MessageBody param) {
        return template.execute(new ControllerCallback<Boolean>() {
            @Override
            public void checkParam() {
                if (Objects.isNull(param.getTargetId())) {
                    throw new CheckParamException("target is null");
                }
                if (StringUtils.isBlank(param.getTemplateCode())) {
                    throw new CheckParamException("templateCode is null");
                }
                // 限流处理，只告警，不阻止
                String rateLimiterKey = "message:sendMessage";
                boolean acquired = rateLimiterUtils.tryAcquire(rateLimiterKey, messageRate, TimeUnit.MINUTES);
                if (!acquired) {
                    log.warn("消息推送频率超限");
                    // 检查是否需要发送告警（距离上次告警是否超过5分钟）
                    sendRateLimitAlertIfNeeded();
                }
            }

            @Override
            public void buildContext() {

            }

            @Override
            public Boolean execute() {
                messageService.processMessage(param);
                return Boolean.TRUE;
            }
        });

    }

    /**
     * 标记已读
     */
    @Operation(summary = "标记已读")
    @PostMapping("/message-center/read")
    public Result<Boolean> read(@RequestBody MarkReadRequest param) {
        messageService.markReadStatus(param);
        return Result.success(true);
    }

    /**
     * 拉取消息数量
     */
    @Operation(summary = "拉取消息数量")
    @PostMapping("/message-center/unread/num")
    public Result<Integer> num(@RequestBody UnReadMessageCountQuery param) {
        int num = messageService.unreadNum(param);
        return Result.success(num);
    }

    /**
     * 预拉取消息，分组数量
     */
    @Operation(summary = "预拉取消息，分组数量")
    @PostMapping("/message-center/group/list")
    public Result<PageData<MessageGroupVo>> groupList(@RequestBody UnReadMessageCountQuery param) {
        PageData<MessageGroupVo> vos = messageService.groupList(param);
        return Result.success(vos);
    }

    /**
     * 按组拉取消息
     */
    @Operation(summary = "按组拉取消息")
    @PostMapping("/message-center/messages")
    public Result<PageData<MessageListItemVo>> messages(@RequestBody MessageQuery param) {
        PageData<MessageListItemVo> page = messageService.messages(param);
        return Result.success(page);
    }

    private void sendRateLimitAlertIfNeeded() {
        String alertKey = "log_push_alert_timestamp:message-center";
        RBucket<Long> lastAlertTimeBucket = redissonClient.getBucket(alertKey);
        long currentTime = System.currentTimeMillis();

        // 获取上次告警时间，如果不存在则设置为0
        long lastAlertTime = lastAlertTimeBucket.isExists() ? lastAlertTimeBucket.get() : 0;

        // 如果距离上次告警超过5分钟或者是首次告警
        if (currentTime - lastAlertTime > ALERT_INTERVAL_MS) {
            try {
                // 构建告警消息
                LogMsgDTO alertMsg = new LogMsgDTO();
                alertMsg.setAppName("pheidi");
                alertMsg.setModule("monitor");
                alertMsg.setSubject("限流告警");
                alertMsg.setLogMsg("【限流告警】接口 " + "message-center/sendMessage" + " 接口调用频率超限，请检查客户端是否存在异常");
                alertMsg.setLogTime(String.valueOf(currentTime));
                // 发送告警消息
                dingTalkRobotApi.send(alertMsg);

                log.info("发送限流告警成功，接口={}", "message-center/sendMessage");
            } catch (Exception e) {
                log.error("发送限流告警失败，接口={}", "message-center/sendMessage", e);
            } finally {
                // 更新最后一次告警时间
                lastAlertTimeBucket.set(currentTime);
            }
        } else {
            // 距离上次告警未超过5分钟，不发送告警
            log.debug("限流告警频率控制，距离上次告警时间{}ms，接口={}",
                    (currentTime - lastAlertTime), "message-center/sendMessage");
        }
    }
}
