package com.chargebolt.pheidi.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.chargebolt.pheidi.dto.MapDTO;
import com.chargebolt.pheidi.enums.MsgBizTypeEnum;
import com.chargebolt.pheidi.enums.MsgPushChannelEnum;
import com.chargebolt.pheidi.enums.RedirectGoalEnum;
import com.chargebolt.pheidi.request.MessageTemplateChangeStatus;
import com.chargebolt.pheidi.request.MessageTemplateCreate;
import com.chargebolt.pheidi.request.MessageTemplateEdit;
import com.chargebolt.pheidi.request.MessageTemplateQuery;
import com.chargebolt.pheidi.response.MessageTemplateDetailResp;
import com.chargebolt.pheidi.response.MessageTemplateListItemResp;
import com.chargebolt.pheidi.response.PageData;
import com.chargebolt.pheidi.service.MessageTemplateService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import so.dian.mofa3.lang.domain.Result;

/**
 * 消息模板-业务操作
 */
@Tag(name = "消息模板")
@RestController
public class MessageTemplateController {

    @Resource
    private MessageTemplateService messageTemplateService;

    /**
     * 新增模板
     */
    @Operation(summary = "新增模板")
    @PostMapping("/message-template/create")
    public Result<Long> create(@RequestBody MessageTemplateCreate param) {
        Long templateId = messageTemplateService.create(param);
        return Result.success(templateId);
    }

    /**
     * 编辑模板
     */
    @Operation(summary = "编辑模板")
    @PostMapping("/message-template/edit")
    public Result<Boolean> edit(@RequestBody MessageTemplateEdit param) {
        Boolean success = messageTemplateService.edit(param);
        return Result.success(success);
    }

    // 启用和停用模板
    @Operation(summary = "启用和停用模板")
    @PostMapping("/message-template/changeStatus")
    public Result<Boolean> changeStatus(@RequestBody MessageTemplateChangeStatus param) {
        Boolean success = messageTemplateService.changeStatus(param);
        return Result.success(success);
    }

    /**
     * 模板列表
     */
    @Operation(summary = "模板列表")
    @PostMapping("/message-template/list")
    public Result<PageData<MessageTemplateListItemResp>> list(@RequestBody MessageTemplateQuery param) {
        PageData<MessageTemplateListItemResp> pageData = messageTemplateService.list(param);
        return Result.success(pageData);
    }

    /**
     * 模板详情
     */
    @Operation(summary = "模板详情")
    @GetMapping("/message-template/detail/{id}")
    public Result<MessageTemplateDetailResp> detail(@PathVariable Long id) {
        MessageTemplateDetailResp detail = messageTemplateService.detail(id);
        return Result.success(detail);
    }

    @GetMapping("/message-template/templateCodeNameMap")
    public Result<List<MapDTO>> templateCodeNameMap() {
        List<MapDTO> map = messageTemplateService.templateCodeNameMap();
        return Result.success(map);
    }

    /**
     * 发送通道枚举
     */
    @Operation(summary = "发送通道枚举")
    @GetMapping("/message-template/channel-push/select-items")
    public Result<List<MapDTO>> channelPushSelectItems() {
        return Result.success(MsgPushChannelEnum.getSelectItems());
    }

    /**
     * 业务类型枚举
     */
    @Operation(summary = "业务类型枚举")
    @GetMapping("/message-template/biz-type/select-items")
    public Result<List<MapDTO>> bizTypeSelectItems() {
        return Result.success(MsgBizTypeEnum.getSelectItems());
    }

    /**
     * 跳转目标枚举
     */
    @Operation(summary = "跳转目标枚举")
    @GetMapping("/message-template/redirect-goal/select-items")
    public Result<List<MapDTO>> redirectGoalSelectItems() {
        return Result.success(RedirectGoalEnum.getSelectItems());
    }
}
