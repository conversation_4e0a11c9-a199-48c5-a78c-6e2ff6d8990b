package com.chargebolt.pheidi.service.job;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.pheidi.dao.MessageChannelDao;
import com.chargebolt.pheidi.dao.MessageDao;
import com.chargebolt.pheidi.domain.Message;
import com.chargebolt.pheidi.domain.MessageChannel;
import com.chargebolt.pheidi.service.MessagePushService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class MessagePushJob {

    private static final int MAX_RETRY_COUNT = 3;
    private static final int BATCH_SIZE = 100;
    private static final long MAX_EXECUTION_TIME_MS = 180000; // 3分钟的最大执行时间
    private static final String RETRY_TASK_LOCK_KEY = "pheidi:message:retry_task_lock";
    private static final long LOCK_LEASE_TIME = 4 * 60; // 锁持有时间，单位秒，设置为4分钟

    @Resource
    private MessageChannelDao messageChannelDao;

    @Resource
    private MessageDao messageDao;

    @Resource
    private MessagePushService messagePushService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 定时重试失败的消息推送任务
     * 
     * 功能说明：
     * 1. 每5分钟执行一次，通过分布式锁确保集群中只有一个实例执行
     * 2. 查询 message_channel 表中状态为待推送(0)或推送失败(2)且重试次数未超限的记录
     * 3. 根据 message_channel 中的 message_id 关联查询 message 表获取消息详情
     * 4. 分批处理消息渠道记录，每批最多处理 BATCH_SIZE 条
     * 5. 单次任务最大执行时间为3分钟，防止长时间占用资源
     * 6. 使用消息推送服务进行重试，并更新重试状态
     * 
     * 表关系说明：
     * - message 表：存储消息的详细信息
     * - message_channel 表：存储消息的推送渠道状态，与 message 表是一对多关系
     * 
     * 执行流程：
     * 1. 尝试获取分布式锁，获取失败则直接返回
     * 2. 分页查询 message_channel 表中待重试的记录
     * 3. 对每条记录，根据 message_id 查询 message 表获取消息详情
     * 4. 调用消息推送服务进行重试
     * 5. 更新 message_channel 表中的重试次数和错误信息
     * 6. 释放分布式锁
     * 
     * 注意事项：
     * 1. 使用 Redisson 实现分布式锁，锁超时时间为4分钟
     * 2. 单次任务最大执行3分钟，避免长时间占用资源
     * 3. 最大重试次数由 MAX_RETRY_COUNT 控制
     * 4. 使用异步线程池执行消息推送，避免阻塞定时任务
     * 5. 需要确保 message 和 message_channel 表的数据一致性
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void retryFailedMessages() {
        log.info("尝试获取分布式锁执行重试任务");
        RLock lock = redissonClient.getLock(RETRY_TASK_LOCK_KEY);
        
        try {
            // 尝试立即获取锁，不等待，如果获取不到立即返回false
            boolean isLocked = lock.tryLock(0, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (!isLocked) {
                log.info("未能获取到分布式锁，跳过本次任务执行");
                return;
            }
            
            log.info("成功获取分布式锁，开始扫描待重试的消息");
            long startTime = System.currentTimeMillis();
            try {
                int offset = 0;
                while (true) {
                // 检查执行时间是否超过3分钟
                if (System.currentTimeMillis() - startTime > MAX_EXECUTION_TIME_MS) {
                    log.warn("任务执行时间超过3分钟，退出执行，等待下次调度");
                    break;
                }

                // 分批查询待推送的消息渠道记录
                List<MessageChannel> pendingChannels = messageChannelDao.queryPendingChannels(MAX_RETRY_COUNT, offset,
                        BATCH_SIZE);
                if (pendingChannels.isEmpty()) {
                    break;
                }

                for (MessageChannel channel : pendingChannels) {
                    try {
                        // 获取消息详情
                        Message message = messageDao.getById(channel.getMessageId());
                        if (message == null) {
                            log.error("消息不存在，messageId: {}", channel.getMessageId());
                            continue;
                        }

                        // 重试推送消息
                        messagePushService.pushMessage(message,
                                JSON.toJSONString(Collections.singletonList(channel.getChannel())));
                    } catch (Exception e) {
                        log.error("消息重试推送失败，messageId: {}, channelId: {}, error: {}",
                                channel.getMessageId(), channel.getId(), e.getMessage());
                        // 更新重试次数和错误信息
                        String errorMsg = e.getMessage();
                        if (errorMsg != null && errorMsg.length() > 256) {
                            errorMsg = errorMsg.substring(0, 256);
                        }
                        messageChannelDao.updateRetryCount(channel.getId(), errorMsg, LocalDateTime.now(),
                                System.currentTimeMillis());
                    }
                }
                offset += BATCH_SIZE;
                }
            } catch (Exception e) {
                log.error("扫描待重试消息失败", e);
            }
            log.info("扫描待重试的消息完成，耗时: {}ms", System.currentTimeMillis() - startTime);
        } catch (InterruptedException e) {
            log.error("获取分布式锁过程中被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 释放锁，但只有在当前实例持有锁的情况下才释放
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("分布式锁已释放");
            }
        }
    }
}