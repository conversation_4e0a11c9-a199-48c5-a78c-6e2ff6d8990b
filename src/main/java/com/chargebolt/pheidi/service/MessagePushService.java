package com.chargebolt.pheidi.service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chargebolt.pheidi.domain.Message;
import com.chargebolt.pheidi.enums.MsgPushChannelEnum;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class MessagePushService {

    @Resource
    private Map<String, ChannelPushStrategy> strategyMap;

    /**
     * 异步推送消息，使用专用线程池
     * 
     * @param message 消息对象
     * @param channels 推送渠道列表，JSON格式
     */
    @Async("messagePushExecutor")
    public void pushMessage(Message message, String channels) {
        log.info("开始异步推送消息, messageId: {}", message.getId());
        List<Integer> channelList = JSON.parseObject(channels, new TypeReference<List<Integer>>() {
        });
        for (Integer channel : channelList) {
            String beanName = MsgPushChannelEnum.getBeanName(channel);
            strategyMap.get(beanName).push(message);
        }
    }
}