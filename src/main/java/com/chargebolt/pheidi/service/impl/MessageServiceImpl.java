package com.chargebolt.pheidi.service.impl;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.chargebolt.pheidi.common.MessageReadStatusEnum;
import com.chargebolt.pheidi.common.MessageStatusEnum;
import com.chargebolt.pheidi.request.MarkReadRequest;
import com.chargebolt.pheidi.request.MessageQuery;
import com.chargebolt.pheidi.request.UnReadMessageCountQuery;
import com.chargebolt.pheidi.response.MessageGroupVo;
import com.chargebolt.pheidi.response.MessageListItemVo;
import com.chargebolt.pheidi.response.MsgItem;
import com.chargebolt.pheidi.response.PageData;
import com.chargebolt.pheidi.dao.MessageDao;
import com.chargebolt.pheidi.dao.MessageReadStatusDao;
import com.chargebolt.pheidi.dao.MessageTemplateDao;
import com.chargebolt.pheidi.domain.Message;
import com.chargebolt.pheidi.domain.MessageReadStatus;
import com.chargebolt.pheidi.domain.MessageTemplate;
import com.chargebolt.pheidi.request.MessageBody;
import com.chargebolt.pheidi.service.MessagePushService;
import com.chargebolt.pheidi.service.MessageService;
import com.google.common.collect.Lists;
import com.chargebolt.pheidi.enums.MsgBizTypeEnum;
import com.chargebolt.pheidi.enums.TemplateCodeParamsEnum;
import com.chargebolt.pheidi.enums.TemplateCodeParamsEnum.ParamEnum;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class MessageServiceImpl implements MessageService {

    @Resource
    private MessageTemplateDao templateDao;
    @Resource
    private MessageDao messageDao;
    @Resource
    private MessageReadStatusDao messageReadStatusDao;
    @Resource
    private MessagePushService messagePushService;

    @Override
    public void processMessage(MessageBody param) {
        MessageTemplate template = templateDao.getMessageTemplateByCode(param.getTemplateCode());
        if (template == null) {
            log.error("消息模板不存在");
            return;
        }
        if (template.getStatus() != 1) {
            log.error("消息模板已禁用");
            return;
        }

        // 根据消息模板生成消息内容
        // 使用 SpEL 替换模板内容中的占位符
        String formattedContent = replacePlaceholdersWithSpEL(template.getContent(), param.getContentVariable());
        // 构建消息对象
        Message message = buildMessage(param, template, formattedContent);
        // 持久化消息
        messageDao.insertMessage(message);
        // 推送消息
        messagePushService.pushMessage(message, template.getChannels());
    }

    /**
     * 构建消息对象
     *
     * @param param    消息参数
     * @param template 消息模板
     * @return 消息对象
     */
    private Message buildMessage(MessageBody param, MessageTemplate template, String formattedContent) {
        Message message = new Message();
        message.setBizType(template.getBizType());
        message.setTitle(template.getTitle());
        if (MsgBizTypeEnum.STORE.getCode().equals(template.getBizType())
                && StringUtils.isNotBlank(template.getRedirectUrl())) {
            // 从 param中获取值，替换到 redirectUrl 中的{}占位符中
            String shopId = param.getContentVariable().get(ParamEnum.shop_id.getParam());
            message.setRedirectUrl(template.getRedirectUrl().replace("{}", shopId));
        } else if (MsgBizTypeEnum.DEVICE.getCode().equals(template.getBizType())
                && StringUtils.isNotBlank(template.getRedirectUrl())) {
            // 从 param中获取值，替换到 redirectUrl 中的{}占位符中
            String deviceNo = param.getContentVariable()
                    .get(param.getContentVariable().get(ParamEnum.device_no.getParam()));
            message.setRedirectUrl(template.getRedirectUrl().replace("{}", deviceNo));
        } else {
            // 其他业务类型，直接使用 redirectUrl
            message.setRedirectUrl(template.getRedirectUrl());
        }
        message.setRedirectGoal(template.getRedirectGoal());
        message.setTemplateId(template.getId());
        message.setStatus(MessageStatusEnum.WAIT_PUSH.getStatus());
        message.setExpireTime(LocalDateTime.now().plusDays(1));
        message.setPriority(0);
        message.setContent(formattedContent);
        message.setGenerateTime(
                LocalDateTime.ofInstant(Instant.ofEpochMilli(param.getCreateTime()), ZoneId.systemDefault()));
        message.setTargetType(param.getTargetType());
        message.setTargetValue(param.getTargetId());
        message.setGmtCreate(System.currentTimeMillis());
        message.setGmtUpdate(System.currentTimeMillis());
        message.setDeleted(0);
        return message;
    }

    private String replacePlaceholdersWithSpEL(String template, Map<String, String> variables) {
        if (template == null || variables == null || variables.isEmpty()) {
            return template;
        }
        // tenplate 要转换成 spel 能识别的字符串，例如 "门店${shopId}的等级${shopLevel}太低了，快去维护下" 要转换成
        // "'门店' + #shopId + '的等级' + #shopLevel + '太低了，快去维护下'"
        String spelTemplate = template.replaceAll("\\$\\{([^}]+)\\}", "'+#$1+'");
        spelTemplate = "'" + spelTemplate + "'";
        log.info("spelTemplate: {}", spelTemplate);
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();
        // 将变量逐个添加到上下文中
        variables.forEach(context::setVariable);
        // 使用 SpEL 解析模板字符串
        return parser.parseExpression(spelTemplate).getValue(context, String.class);
    }

    /**
     * 标记已读
     *
     * @param request
     */
    @Override
    public void markReadStatus(MarkReadRequest param) {
        // 1. 参数校验
        if (param == null || param.getTargetId() == null) {
            throw new IllegalArgumentException("param or targetId cannot be null");
        }

        // 2. 批量更新消息状态为已读
        int updatedCount = messageReadStatusDao.batchUpdateStatus(
                param.getTargetId().toString(),
                param.getBizType(),
                MessageReadStatusEnum.READ.getStatus());

        log.info("Mark messages as read, targetId: {}, bizType: {}, updatedCount: {}",
                param.getTargetId(), param.getBizType(), updatedCount);
    }

    @Override
    public int unreadNum(UnReadMessageCountQuery param) {
        return messageReadStatusDao.unreadNum(param.getExternalId());
    }

    @Override
    public PageData<MessageGroupVo> groupList(UnReadMessageCountQuery param) {
        // 1. 参数校验
        if (param == null || param.getExternalId() == null) {
            throw new IllegalArgumentException("param or externalId cannot be null");
        }

        // 2. 查询各业务类型的未读数量
        List<Map<String, Integer>> unreadCounts = messageReadStatusDao.countUnreadByBizType(param.getExternalId());
        Map<Integer, Long> unreadCountMap = unreadCounts.stream()
                .collect(Collectors.toMap(
                        map -> (Integer) map.get("biz_type"),
                        map -> ((Number) map.get("unread_count")).longValue()));

        log.info("Unread counts: {}", unreadCountMap);
        // 2. 查询各业务类型的总数量
        List<Map<String, Integer>> totalCounts = messageReadStatusDao.countTotalByBizType(param.getExternalId());
        Map<Integer, Long> totalCountMap = totalCounts.stream()
                .collect(Collectors.toMap(
                        map -> (Integer) map.get("biz_type"),
                        map -> ((Number) map.get("total_count")).longValue()));
        log.info("Total counts: {}", totalCountMap);

        // 3. 查询各业务类型的最新消息
        List<MessageReadStatus> latestMessages = Lists.newArrayList();
        for (MsgBizTypeEnum bizType : MsgBizTypeEnum.values()) {
            latestMessages
                    .add(messageReadStatusDao.queryLatestMessageByBizType(param.getExternalId(), bizType.getCode()));
        }

        List<Long> messageIds = latestMessages.stream().filter(Objects::nonNull).map(MessageReadStatus::getMessageId)
                .collect(Collectors.toList());
        Map<Integer, Message> bizTypeToLatestMessage = new HashMap<>();
        if (!CollectionUtils.isEmpty(messageIds)) {
            List<Message> messages = messageDao.getByIds(messageIds);
            bizTypeToLatestMessage = messages.stream()
                    .collect(Collectors.toMap(Message::getBizType, m -> m, (x, y) -> x));
        }

        final Map<Integer, Message> finalBizTypeToLatestMessage = bizTypeToLatestMessage;
        // 4. 构建分组结果
        List<MessageGroupVo> groups = Arrays.stream(MsgBizTypeEnum.values())
                .map(bizType -> {
                    Long unreadCount = unreadCountMap.get(bizType.getCode());
                    Message latestMessage = finalBizTypeToLatestMessage.get(bizType.getCode());
                    MsgItem lastMsg = latestMessage != null ? MsgItem.builder()
                            .updateTime(latestMessage.getGenerateTime()
                                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                            .content(latestMessage.getContent())
                            .build() : null;
                    return MessageGroupVo.builder()
                            .bizType(bizType.getCode())
                            .unreadNum(Optional.ofNullable(unreadCount).orElse(0L))
                            .totalNum(Optional.ofNullable(totalCountMap.get(bizType.getCode())).orElse(0L))
                            .bizTypeStr(bizType.getDesc())
                            .iconUrl(bizType.getIconUrl())
                            .lastMsg(lastMsg)
                            .build();
                })
                .collect(Collectors.toList());

        return PageData.create(groups, (long) groups.size());
    }

    @Override
    public PageData<MessageListItemVo> messages(MessageQuery param) {
        // 1. 参数校验
        if (param == null || param.getExternalId() == null) {
            throw new IllegalArgumentException("param or externalId cannot be null");
        }
        if (param.getPageNo() == null || param.getPageNo() < 1) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            param.setPageSize(10);
        }

        // 2. 查询总数
        int total = messageReadStatusDao.countMessage(param.getExternalId(), param.getBizType());
        if (total <= 0) {
            return PageData.create(Collections.emptyList(), 0L);
        }

        // 3. 查询消息列表
        List<MessageReadStatus> messages = messageReadStatusDao.queryMessageList(
                param.getExternalId(),
                param.getBizType(),
                (param.getPageNo() - 1) * param.getPageSize(),
                param.getPageSize());
        if (messages.isEmpty()) {
            return PageData.create(Collections.emptyList(), 0L);
        }
        List<Long> messageIds = messages.stream().map(MessageReadStatus::getMessageId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(messageIds)) {
            return PageData.create(Collections.emptyList(), 0L);
        }
        List<Message> messageList = messageDao.getByIds(messageIds);

        // 4. 转换为响应对象
        List<MessageListItemVo> items = messageList.stream()
                .map(this::convertToMessageItem)
                .collect(Collectors.toList());

        // 5. 构建分页结果
        return PageData.create(items, (long) total, (long) param.getPageNo(), param.getPageSize());
    }

    private MessageListItemVo convertToMessageItem(Message message) {
        return MessageListItemVo.builder()
                .id(message.getId())
                .title(message.getTitle())
                .content(message.getContent())
                .updateTime(message.getGenerateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .targetUrl(message.getRedirectUrl())
                .build();
    }
}
