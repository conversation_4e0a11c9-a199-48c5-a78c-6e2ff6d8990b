package com.chargebolt.pheidi.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.chargebolt.pheidi.common.TemplateStatusEnum;
import com.chargebolt.pheidi.request.MessageTemplateChangeStatus;
import com.chargebolt.pheidi.request.MessageTemplateCreate;
import com.chargebolt.pheidi.request.MessageTemplateEdit;
import com.chargebolt.pheidi.request.MessageTemplateQuery;
import com.chargebolt.pheidi.response.MessageTemplateDetailResp;
import com.chargebolt.pheidi.response.MessageTemplateListItemResp;
import com.chargebolt.pheidi.response.PageData;
import com.chargebolt.pheidi.dao.MessageTemplateDao;
import com.chargebolt.pheidi.domain.MessageTemplate;
import com.chargebolt.pheidi.dto.MapDTO;
import com.chargebolt.pheidi.enums.MsgBizTypeEnum;
import com.chargebolt.pheidi.service.MessageTemplateService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class MessageTemplateServiceImpl implements MessageTemplateService {

    @Resource
    private MessageTemplateDao messageTemplateDao;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 新增模板
     * 使用 redisson 增加分布式锁，防止并发创建。lock key 为
     * pheidi:template:create:${code},获取锁失败则抛异常。锁过期时间10秒，或finally释放锁
     * 
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(MessageTemplateCreate param) {
        // 1. 参数校验
        if (param == null) {
            throw new IllegalArgumentException("param cannot be null");
        }

        String lockKey = String.format("pheidi:template:create:%s", param.getName());
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，等待0秒，10秒后自动释放
            boolean isLocked = lock.tryLock(0, 10, TimeUnit.SECONDS);
            if (!isLocked) {
                throw new RuntimeException("获取分布式锁失败，请稍后重试");
            }

            // 生成8位大写随机字符串，冲突概率为一亿分之一
            String templateCode = NanoIdUtils.randomNanoId(NanoIdUtils.DEFAULT_NUMBER_GENERATOR,
                    NanoIdUtils.DEFAULT_ALPHABET, 8).toUpperCase();

            // 兜底，万一冲突了，抛出异常。
            MessageTemplate existTemplate = messageTemplateDao.getMessageTemplateByCode(templateCode);
            if (existTemplate != null) {
                throw new IllegalArgumentException("template code already exists");
            }

            // 校验模板名称是否存在
            existTemplate = messageTemplateDao.getMessageTemplateByName(param.getName());
            if (existTemplate != null) {
                throw new IllegalArgumentException("template name already exists");
            }

            // 3. 构建消息模板实体
            MessageTemplate template = new MessageTemplate();
            template.setName(param.getName());
            template.setCode(templateCode);
            template.setBizType(param.getType());
            template.setChannels(JSON.toJSONString(param.getChannel()));
            template.setTitle(param.getTitle());
            template.setContent(param.getContent());
            template.setRedirectGoal(param.getRedirectGoal());
            template.setRedirectUrl(param.getRedirectLink());
            template.setStatus(TemplateStatusEnum.ENABLE.getStatus());
            template.setGmtCreate(System.currentTimeMillis());
            template.setGmtUpdate(template.getGmtCreate());
            template.setDeleted(0);
            // 4. 保存模板
            messageTemplateDao.insertMessageTemplate(template);

            return template.getId();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取分布式锁被中断", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(MessageTemplateEdit param) {
        // 1. 参数校验
        if (param == null || param.getTemplateId() == null) {
            throw new IllegalArgumentException("param or templateId cannot be null");
        }

        String lockKey = String.format("pheidi:template:edit:%s", param.getTemplateId());
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，等待0秒，10秒后自动释放
            boolean isLocked = lock.tryLock(0, 10, TimeUnit.SECONDS);
            if (!isLocked) {
                throw new RuntimeException("获取分布式锁失败，请稍后重试");
            }
            // 2. 获取现有模板
            MessageTemplate template = messageTemplateDao.getMessageTemplateById(param.getTemplateId());
            if (template == null) {
                throw new IllegalArgumentException("template not found");
            }

            // 如果模板名称有变更，则校验模板名称是否存在
            if (!Objects.equals(param.getName(), template.getName())) {
                MessageTemplate existTemplate = messageTemplateDao.getMessageTemplateByName(param.getName());
                if (existTemplate != null && !existTemplate.getId().equals(param.getTemplateId())) {
                    throw new IllegalArgumentException("template name already exists");
                }
            }

            // 3. 更新模板信息
            template.setName(param.getName());
            template.setBizType(param.getType());
            template.setChannels(JSON.toJSONString(param.getChannel()));
            template.setTitle(param.getTitle());
            template.setContent(param.getContent());
            template.setRedirectGoal(param.getRedirectGoal());
            template.setRedirectUrl(param.getRedirectLink());

            // 4. 保存更新
            int updated = messageTemplateDao.updateMessageTemplate(template);
            return updated > 0;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取分布式锁被中断", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public PageData<MessageTemplateListItemResp> list(MessageTemplateQuery param) {
        // 1. 参数校验和默认值设置
        if (param == null) {
            throw new IllegalArgumentException("param cannot be null");
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            param.setPageSize(10);
        }
        if (param.getPageNo() == null || param.getPageNo() < 1) {
            param.setPageNo(1);
        }

        // 2. 查询总数
        int total = messageTemplateDao.countMessageTemplate(param.getTemplateName(),
                param.getTemplateCode(),
                param.getBizType(),
                param.getStatus());
        if (total <= 0) {
            return PageData.create(Collections.emptyList());
        }

        // 3. 查询列表数据
        List<MessageTemplate> templates = messageTemplateDao.queryMessageTemplateList(param.getTemplateName(),
                param.getTemplateCode(),
                param.getBizType(),
                param.getStatus(),
                (param.getPageNo() - 1) * param.getPageSize(),
                param.getPageSize());
        List<MessageTemplateListItemResp> items = templates.stream()
                .map(this::convertToListItem)
                .collect(Collectors.toList());

        // 4. 构建分页结果
        return PageData.create(items, (long) total, (long) param.getPageNo(), param.getPageSize());
    }

    @Override
    public MessageTemplateDetailResp detail(Long id) {
        // 1. 参数校验
        if (id == null) {
            throw new IllegalArgumentException("id cannot be null");
        }

        // 2. 查询模板
        MessageTemplate template = messageTemplateDao.getMessageTemplateById(id);
        if (template == null) {
            throw new IllegalArgumentException("template not found");
        }

        // 3. 转换为详情响应
        MessageTemplateDetailResp resp = new MessageTemplateDetailResp();
        resp.setTemplateId(template.getId());
        resp.setCode(template.getCode());
        resp.setName(template.getName());
        resp.setType(template.getBizType());

        // 转换渠道列表
        List<Integer> channels = JSON.parseObject(template.getChannels(), new TypeReference<List<Integer>>() {
        });
        resp.setChannel(channels);

        resp.setTitle(template.getTitle());
        resp.setContent(template.getContent());
        resp.setRedirectGoal(template.getRedirectGoal());
        resp.setRedirectLink(template.getRedirectUrl());

        return resp;
    }

    private MessageTemplateListItemResp convertToListItem(MessageTemplate template) {
        MessageTemplateListItemResp resp = new MessageTemplateListItemResp();
        resp.setTemplateId(template.getId());
        resp.setCode(template.getCode());
        resp.setName(template.getName());
        resp.setType(template.getBizType());
        resp.setTypeStr(MsgBizTypeEnum.getByCode(template.getBizType()).getDesc());

        // 转换渠道列表
        List<String> channels = JSON.parseObject(template.getChannels(), new TypeReference<List<String>>() {
        });
        resp.setChannels(channels);

        resp.setStatus(template.getStatus());
        resp.setStatusStr(TemplateStatusEnum.getDesc(template.getStatus()));
        return resp;
    }

    @Override
    public List<MapDTO> templateCodeNameMap() {
        MessageTemplateQuery param = new MessageTemplateQuery();
        param.setStatus(1);
        List<MessageTemplate> templates = messageTemplateDao.queryAllTemplates();
        return templates.stream()
                .filter(Objects::nonNull)
                .map(template -> new MapDTO(template.getCode(), template.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public Boolean changeStatus(MessageTemplateChangeStatus param) {
        // 1. 参数校验
        if (param == null || param.getTemplateId() == null) {
            throw new IllegalArgumentException("param cannot be null");
        }

        // 2. 更新模板状态
        int updated = messageTemplateDao.updateMessageTemplateStatus(param.getTemplateId(),
                Boolean.TRUE.equals(param.getEnable()) ? 1 : 0);
        return updated > 0;
    }
}
