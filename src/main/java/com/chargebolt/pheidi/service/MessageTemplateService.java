package com.chargebolt.pheidi.service;

import java.util.List;

import com.chargebolt.pheidi.dto.MapDTO;
import com.chargebolt.pheidi.request.MessageTemplateChangeStatus;
import com.chargebolt.pheidi.request.MessageTemplateCreate;
import com.chargebolt.pheidi.request.MessageTemplateEdit;
import com.chargebolt.pheidi.request.MessageTemplateQuery;
import com.chargebolt.pheidi.response.MessageTemplateDetailResp;
import com.chargebolt.pheidi.response.MessageTemplateListItemResp;
import com.chargebolt.pheidi.response.PageData;

public interface MessageTemplateService {
    /**
     * 创建消息模板
     *
     * @param param 创建参数
     * @return 模板ID
     */
    Long create(MessageTemplateCreate param);

    /**
     * 编辑消息模板
     *
     * @param param 编辑参数
     * @return 是否成功
     */
    Boolean edit(MessageTemplateEdit param);

    /**
     * 查询消息模板列表
     *
     * @param param 查询参数
     * @return 分页数据
     */
    PageData<MessageTemplateListItemResp> list(MessageTemplateQuery param);

    /**
     * 查询模板详情
     *
     * @param id 模板ID
     * @return 模板详情
     */
    MessageTemplateDetailResp detail(Long id);

    /**
     * 查询模板ID和名称的映射
     *
     * @return 模板ID和名称的映射
     */
    List<MapDTO> templateCodeNameMap();

    /**
     * 启用和停用模板
     *
     * @param param 启用和停用参数
     * @return 是否成功
     */
    Boolean changeStatus(MessageTemplateChangeStatus param);
}
