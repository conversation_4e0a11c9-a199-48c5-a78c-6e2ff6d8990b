package com.chargebolt.pheidi.service;

import com.chargebolt.pheidi.request.MarkReadRequest;
import com.chargebolt.pheidi.request.MessageQuery;
import com.chargebolt.pheidi.request.UnReadMessageCountQuery;
import com.chargebolt.pheidi.response.MessageGroupVo;
import com.chargebolt.pheidi.response.MessageListItemVo;
import com.chargebolt.pheidi.response.PageData;
import com.chargebolt.pheidi.request.MessageBody;

public interface MessageService {

    void processMessage(MessageBody param);

    void markReadStatus(MarkReadRequest request);

    int unreadNum(UnReadMessageCountQuery param);

    PageData<MessageGroupVo> groupList(UnReadMessageCountQuery param);

    PageData<MessageListItemVo> messages(MessageQuery param);
}
