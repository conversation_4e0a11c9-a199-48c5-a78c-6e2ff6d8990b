package com.chargebolt.pheidi.service.strategy;

import java.time.LocalDateTime;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chargebolt.pheidi.common.MessageStatusEnum;
import com.chargebolt.pheidi.dao.MessageChannelDao;
import com.chargebolt.pheidi.dao.MessageDao;
import com.chargebolt.pheidi.dao.MessageReadStatusDao;
import com.chargebolt.pheidi.domain.Message;
import com.chargebolt.pheidi.domain.MessageChannel;
import com.chargebolt.pheidi.domain.MessageReadStatus;
import com.chargebolt.pheidi.enums.MsgPushChannelEnum;
import com.chargebolt.pheidi.service.ChannelPushStrategy;

import lombok.extern.slf4j.Slf4j;

@Slf4j(topic = "InAppChannelPushStrategy")
@Service(value = "inApp")
public class InAppChannelPushStrategy implements ChannelPushStrategy {

    @Resource
    private MessageChannelDao messageChannelDao;
    @Resource
    private MessageReadStatusDao messageReadStatusDao;
    @Resource
    private MessageDao messageDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void push(Message message) {
        // 标记消息的推送状态
        messageDao.updateMessageStatus(message.getId(), MessageStatusEnum.PUSHED.getStatus());
        // 先查询 message_channel 表中是否有记录
        MessageChannel messageChannel = messageChannelDao.selectByMessageIdAndChannel(message.getId(),
                MsgPushChannelEnum.IN_APP.getCode());
        if (messageChannel == null) {
            // 记录推送渠道记录
            messageChannel = buildMessageChannelRecord(message);
            messageChannelDao.insertMessageChannel(messageChannel);
        }

        // 创建未读状态记录
        MessageReadStatus readStatus = buildMessageReadStatus(message);
        messageReadStatusDao.insert(readStatus);

        // 更新推送记录为成功
        messageChannel.setStatus(1); // 推送成功
        messageChannel.setCompletedTime(LocalDateTime.now());
        messageChannel.setGmtUpdate(System.currentTimeMillis());
        messageChannelDao.updateMessageChannel(messageChannel);

        log.info("站内信推送成功，messageId={}", message.getId());

    }

    private MessageChannel buildMessageChannelRecord(Message message) {
        MessageChannel channel = new MessageChannel();
        // 设置基本属性
        channel.setMessageId(message.getId());
        channel.setChannel(MsgPushChannelEnum.IN_APP.getCode());
        channel.setStatus(0); // 待推送状态
        channel.setRetryCount(0); // 初始重试次数为0
        channel.setLastAttemptTime(LocalDateTime.now()); // 当前时间作为最后尝试时间
        channel.setErrorMsg(null); // 初始无错误信息
        // 设置目标信息
        channel.setTargetType(message.getTargetType());
        channel.setTargetValue(message.getTargetValue());
        // 设置时间相关字段
        channel.setScheduledTime(LocalDateTime.now()); // 默认立即发送
        channel.setCompletedTime(null); // 未完成
        // 设置通用字段
        long currentTime = System.currentTimeMillis();
        channel.setGmtCreate(currentTime);
        channel.setGmtUpdate(currentTime);
        channel.setDeleted(0); // 未删除
        return channel;
    }

    private MessageReadStatus buildMessageReadStatus(Message message) {
        MessageReadStatus readStatus = new MessageReadStatus();
        readStatus.setMessageId(message.getId());
        readStatus.setUserId(message.getTargetValue()); // 假设targetValue存储的是用户ID
        readStatus.setChannel(MsgPushChannelEnum.IN_APP.getCode());
        readStatus.setReadTime(null); // 未读状态，readTime为null
        readStatus.setStatus(0); // 未读状态，status为0
        readStatus.setBizType(message.getBizType());
        // 设置通用字段
        long currentTime = System.currentTimeMillis();
        readStatus.setGmtCreate(currentTime);
        readStatus.setGmtUpdate(currentTime);
        readStatus.setDeleted(0);
        return readStatus;
    }
}
