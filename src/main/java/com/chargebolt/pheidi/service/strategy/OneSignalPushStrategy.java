package com.chargebolt.pheidi.service.strategy;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.chargebolt.pheidi.common.MessageStatusEnum;
import com.chargebolt.pheidi.dao.MessageChannelDao;
import com.chargebolt.pheidi.dao.MessageDao;
import com.chargebolt.pheidi.domain.Message;
import com.chargebolt.pheidi.domain.MessageChannel;
import com.chargebolt.pheidi.enums.MsgPushChannelEnum;
import com.chargebolt.pheidi.enums.RedirectGoalEnum;
import com.chargebolt.pheidi.remote.OneSignalApi;
import com.chargebolt.pheidi.remote.request.ExtraData;
import com.chargebolt.pheidi.remote.request.IncludeAliases;
import com.chargebolt.pheidi.remote.request.OneSignalSendRequest;
import com.chargebolt.pheidi.remote.response.OneSignalSendResponse;
import com.chargebolt.pheidi.service.ChannelPushStrategy;

import lombok.extern.slf4j.Slf4j;

@Slf4j(topic = "OneSignalPushStrategy")
@Service(value = "oneSignal")
public class OneSignalPushStrategy implements ChannelPushStrategy {

    @Resource
    private OneSignalApi oneSignalApi;
    @Value("${onesignal.apikey}")
    private String apikey;
    @Value("${onesignal.appid}")
    private String appid;
    @Value("${message.redirectUrl}")
    private String redirectUrl;
    @Resource
    private MessageChannelDao messageChannelDao;
    @Resource
    private MessageDao messageDao;

    private HashMap<String, String> builderHeader() {
        HashMap<String, String> header = new HashMap<>(2);
        String authorization = "Key " + apikey;
        header.put("Authorization", authorization);
        header.put("Content-Type", "application/json");
        header.put("accept", "application/json");
        return header;
    }

    /**
     * example response:{"id":"","errors":["All included players are not
     * subscribed"]}
     */
    @Override
    public void push(Message message) {
        // 标记消息的推送状态
        messageDao.updateMessageStatus(message.getId(), MessageStatusEnum.PUSHED.getStatus());
        // 先查询 message_channel 表中是否有记录
        MessageChannel messageChannel = messageChannelDao.selectByMessageIdAndChannel(message.getId(),
                MsgPushChannelEnum.ONE_SIGNAL.getCode());
        if (messageChannel == null) {
            // 记录推送渠道记录
            messageChannel = buildMessageChannelRecord(message);
            messageChannelDao.insertMessageChannel(messageChannel);
        }

        OneSignalSendRequest request = buildOneSignalRequest(message);
        OneSignalSendResponse result = oneSignalApi.sendMessage(request, builderHeader());
        log.info("oneSignalApi.sendMessage result={}", JSON.toJSONString(result));
        if (StringUtils.isNotBlank(result.getId())) {
            log.info("oneSignalApi.sendMessage success, messageId={}, result.id={}", message.getId(), result.getId());
            // 更改推送记录状态为推送成功
            messageChannel.setStatus(1);
            messageChannel.setCompletedTime(LocalDateTime.now());
            messageChannel.setGmtUpdate(System.currentTimeMillis());
            messageChannelDao.updateMessageChannel(messageChannel);
        } else {
            log.error("oneSignalApi.sendMessage exception, messageId={}, result={}", message.getId(),
                    JSON.toJSONString(result));
            // 更新推送记录状态为推送失败
            messageChannel.setStatus(2);
            // 这个字段暂时不用，定时任务每 5 分钟执行一次，最多执行 3 次
            messageChannel.setScheduledTime(null);
            messageChannel.setRetryCount(messageChannel.getRetryCount() + 1);
            messageChannel.setErrorMsg(JSON.toJSONString(result.getErrors()));
            messageChannel.setGmtUpdate(System.currentTimeMillis());
            messageChannelDao.updateMessageChannel(messageChannel);
        }
    }

    private MessageChannel buildMessageChannelRecord(Message message) {
        MessageChannel channel = new MessageChannel();
        // 设置基本属性
        channel.setMessageId(message.getId());
        channel.setChannel(MsgPushChannelEnum.ONE_SIGNAL.getCode());
        channel.setStatus(0); // 待推送状态
        channel.setRetryCount(0); // 初始重试次数为0
        channel.setLastAttemptTime(LocalDateTime.now()); // 当前时间作为最后尝试时间
        channel.setErrorMsg(null); // 初始无错误信息
        // 设置目标信息
        channel.setTargetType(message.getTargetType());
        channel.setTargetValue(message.getTargetValue());
        // 设置时间相关字段
        channel.setScheduledTime(LocalDateTime.now()); // 默认立即发送
        channel.setCompletedTime(null); // 未完成
        // 设置通用字段
        long currentTime = System.currentTimeMillis();
        channel.setGmtCreate(currentTime);
        channel.setGmtUpdate(currentTime);
        channel.setDeleted(0); // 未删除
        return channel;
    }

    private OneSignalSendRequest buildOneSignalRequest(Message message) {
        OneSignalSendRequest request = new OneSignalSendRequest();
        request.setAppId(appid);
        request.setTargetChannel("push");
        Map<String, String> headings = new HashMap<>();
        headings.put("en", message.getTitle());
        request.setHeadings(headings);
        Map<String, String> content = new HashMap<>();
        content.put("en", message.getContent());
        request.setContents(content);
        request.setIncludeAliases(new IncludeAliases(Collections.singletonList(message.getTargetValue().toString())));
        if (RedirectGoalEnum.MESSAGE_CENTER.getCode().equals(message.getRedirectGoal())) {
            // 如果 redirectGoal 是消息中心，这里设置成消息中心的链接。
            request.setExtraData(ExtraData.builder().page(redirectUrl).build());
        } else {
            // 如果是业务详情，这里就填 redirectUrl
            request.setExtraData(ExtraData.builder().page(message.getRedirectUrl()).build());
        }
        return request;
    }
}
