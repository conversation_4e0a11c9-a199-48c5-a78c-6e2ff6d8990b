package com.chargebolt.pheidi.util;

import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 限流工具类
 * 基于Redisson的RRateLimiter实现分布式限流
 */
@Slf4j
@Component
public class RateLimiterUtils {

    @Resource
    private RedissonClient redissonClient;

    @Value("${rateLimiter.rate-interval}")
    private int rateInterval;

    private static final String RATE_LIMITER_PREFIX = "rate_limiter:";

    /**
     * 尝试获取令牌
     * 
     * @param key      限流标识
     * @param rate     速率
     * @param timeUnit 时间单位
     * @return 是否获取成功
     */
    public boolean tryAcquire(String key, long rate, TimeUnit timeUnit) {
        String rateLimiterKey = RATE_LIMITER_PREFIX + key;
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(rateLimiterKey);

        // 初始化限流器，如果已经存在则不会重新初始化
        if (!rateLimiter.isExists()) {
            // 创建令牌桶：每个时间单位产生rate个令牌
            boolean initialized = rateLimiter.trySetRate(RateType.OVERALL, rate,
                    rateInterval, convertTimeUnit(timeUnit));
            if (initialized) {
                log.info("初始化限流器成功, key={}, rate={}, rateInterval={}, timeUnit={}", key, rate, rateInterval,
                        timeUnit);
            }
        }

        // 尝试获取一个令牌，立即返回结果
        return rateLimiter.tryAcquire(1);
    }

    /**
     * 将TimeUnit转换为RateIntervalUnit
     */
    private RateIntervalUnit convertTimeUnit(TimeUnit timeUnit) {
        switch (timeUnit) {
            case SECONDS:
                return RateIntervalUnit.SECONDS;
            case MINUTES:
                return RateIntervalUnit.MINUTES;
            case HOURS:
                return RateIntervalUnit.HOURS;
            case DAYS:
                return RateIntervalUnit.DAYS;
            default:
                return RateIntervalUnit.SECONDS;
        }
    }
}
