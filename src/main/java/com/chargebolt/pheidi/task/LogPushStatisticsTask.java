package com.chargebolt.pheidi.task;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.chargebolt.pheidi.dto.LogMsgDTO;
import com.chargebolt.pheidi.remote.DingTalkRobotApi;

import lombok.extern.slf4j.Slf4j;

/**
 * 日志推送统计任务
 * 每天下午6点统计各应用的日志推送次数，并通过钉钉推送统计数据
 */
@Slf4j
@Component
public class LogPushStatisticsTask {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private DingTalkRobotApi dingTalkRobotApi;

    // 请求计数Redis前缀
    private static final String REQUEST_COUNT_PREFIX = "log_push_count:";

    // 分布式锁名称
    private static final String STATISTICS_LOCK_NAME = "log_push_statistics_lock";

    // 应用列表
    private static final List<String> APP_LIST = new ArrayList<String>() {
        {
            add("hera");
            add("kronos");
            add("ezreal");
        }
    };

    /**
     * 每天下午6点执行统计任务
     * 使用分布式锁确保在集群环境中只有一个实例执行
     */
    @Scheduled(cron = "0 0 18 * * ?")
    public void statisticsAndReset() {
        log.info("开始执行日志推送统计任务");

        // 获取分布式锁，无等待，锁持有时间5分钟
        RLock lock = redissonClient.getLock(STATISTICS_LOCK_NAME);
        boolean locked = false;

        try {
            // 尝试获取锁，不等待，锁超时时间5分钟
            locked = lock.tryLock(0, 5 * 60, TimeUnit.SECONDS);
            if (!locked) {
                log.info("未获取到分布式锁，跳过本次统计任务");
                return;
            }

            // 获取并推送统计数据
            collectAndPushStatistics();

        } catch (Exception e) {
            log.error("执行日志推送统计任务异常", e);
        } finally {
            // 释放锁
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 收集并推送统计数据
     */
    private void collectAndPushStatistics() {
        try {
            // 收集各应用的请求次数
            Map<String, Long> appCountMap = new HashMap<>();
            long totalCount = 0;

            for (String appName : APP_LIST) {
                String countKey = REQUEST_COUNT_PREFIX + appName;
                RAtomicLong counter = redissonClient.getAtomicLong(countKey);
                long count = counter.get();
                appCountMap.put(appName, count);
                totalCount += count;

                // 重置计数器
                counter.set(0);
            }

            // 构建统计消息
            String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            StringBuilder content = new StringBuilder();
            content.append("【日志推送统计】").append(date).append("\n\n");
            content.append("总请求次数: ").append(totalCount).append("\n\n");
            content.append("各应用请求次数:\n");

            for (Map.Entry<String, Long> entry : appCountMap.entrySet()) {
                content.append("- ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }

            // 发送统计消息
            LogMsgDTO statsMsg = new LogMsgDTO();
            statsMsg.setAppName("pheidi");
            statsMsg.setModule("statistics");
            statsMsg.setSubject("日志推送统计");
            statsMsg.setLogMsg(content.toString());
            statsMsg.setLogTime(String.valueOf(System.currentTimeMillis()));

            dingTalkRobotApi.send(statsMsg);
            log.info("日志推送统计消息发送成功");

        } catch (Exception e) {
            log.error("收集并推送统计数据异常", e);
        }
    }
}
