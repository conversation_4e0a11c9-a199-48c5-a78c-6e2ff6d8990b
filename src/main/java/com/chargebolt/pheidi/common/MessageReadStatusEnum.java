package com.chargebolt.pheidi.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MessageReadStatusEnum {

    UN_READ(0, "未读"),
    READ(1, "已读"),
    ;

    private Integer status;
    private String desc;

    public static String getDesc(Integer status) {
        for (MessageReadStatusEnum item : MessageReadStatusEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
