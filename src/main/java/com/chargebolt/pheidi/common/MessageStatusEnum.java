package com.chargebolt.pheidi.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MessageStatusEnum {

    WAIT_PUSH(0,"待推送"),
    PUSHED(1,"已推送"),
    ;

    private Integer status;
    private String desc;

    public static String getDesc(Integer status){
        for(MessageStatusEnum item:MessageStatusEnum.values()){
            if(item.getStatus().equals(status)){
                return item.getDesc();
            }
        }
        return null;
    }
}
