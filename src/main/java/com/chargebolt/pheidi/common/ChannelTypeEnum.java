package com.chargebolt.pheidi.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 消息渠道类型枚举
 */
@Getter
@AllArgsConstructor
public enum ChannelTypeEnum {
    STATION_MESSAGE(1, "inApp", "App内推送"),
    ONE_SIGNAL(2, "oneSignal", "OneSignal推送"),
    ;

    private final Integer code;
    private final String beanName;
    private final String desc;

    /**
     * 根据渠道类型获取对应的策略实现类bean名称
     *
     * @param channel 渠道类型
     * @return 策略实现类bean名称
     */
    public static String getBeanName(Integer channel) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(channel))
                .findFirst()
                .map(ChannelTypeEnum::getBeanName)
                .orElseThrow(() -> new IllegalArgumentException("Invalid channel type: " + channel));
    }

    /**
     * 根据渠道类型获取描述
     *
     * @param channel 渠道类型
     * @return 渠道描述
     */
    public static String getDesc(Integer channel) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(channel))
                .findFirst()
                .map(ChannelTypeEnum::getDesc)
                .orElse("未知渠道");
    }
}
