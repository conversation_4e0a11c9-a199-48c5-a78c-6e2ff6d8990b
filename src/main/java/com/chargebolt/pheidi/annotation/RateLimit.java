package com.chargebolt.pheidi.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 限流注解
 * 用于接口限流，基于Redisson的RRateLimiter实现
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    
    /**
     * 限流资源的名称
     * @return 资源名称
     */
    String key() default "";
    
    /**
     * 限流速率，每秒允许的请求数
     * @return 速率
     */
    double rate() default 10;
    
    /**
     * 超过限流速率时的错误消息
     * @return 错误消息
     */
    String message() default "请求太频繁，请稍后再试";
    
    /**
     * 时间单位
     * @return 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;
}
