package com.chargebolt.pheidi.remote;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.pheidi.dto.LogMsgDTO;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.taobao.api.ApiException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class DingTalkRobotApi {

    @Value("${pheidi.profile:dev}")
    private String env;

    private static final DingTalkClient client = new DefaultDingTalkClient(
                "https://oapi.dingtalk.com/robot/send?access_token=c5b0b5f580b2628082b79d582285d7083fe75b742091fb7b9a875c602d8e6a7e");
                
    private static final DingTalkClient devClient = new DefaultDingTalkClient(
                "https://oapi.dingtalk.com/robot/send?access_token=5a53aa7373741e5c94f9b472ebc9c2e0d7d86f035d2ca1906c97cd310fd83732");

    public void send(LogMsgDTO dto) {
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle("海外业务告警");
        markdown.setText(getMarkdownText(dto, env));
        request.setMarkdown(markdown);
        try {
            OapiRobotSendResponse response = env.equals("dev") ? devClient.execute(request) : client.execute(request);
            log.info(response.getBody());
        } catch (ApiException e) {
            log.error("dingtalk error", e);
        }
    }

    /**
     * 生成钉钉机器人markdown格式消息
     * 
     * @param dto 日志消息对象
     * @param env 环境信息
     * @return 格式化后的markdown文本
     */
    private String getMarkdownText(LogMsgDTO dto, String env) {
        String template = "#### %s -【%s】\n" +
                "> **环境**: **%s** \n\n" +
                "> **问题**: %s\n\n" +
                "> **原因**: %s\n\n" +
                "> **时间**: %s\n\n" +
                "> **扩展信息**: %s\n";
        
        // 处理可能包含换行符的字段，确保不破坏markdown格式
        String subject = processMarkdownText(dto.getSubject());
        String logMsg = processMarkdownText(dto.getLogMsg());
        String extraInfo = processMarkdownText(JSON.toJSONString(dto.getExtra()));
        
        return String.format(template, 
                dto.getAppName(),
                dto.getModule(),
                env,
                subject,
                logMsg,
                dto.getLogTime(),
                extraInfo);
    }
    
    /**
     * 处理可能包含换行符和特殊Markdown字符的文本，确保在markdown中正确显示
     * 
     * @param text 需要处理的文本
     * @return 处理后的文本
     */
    private String processMarkdownText(String text) {
        if (text == null) {
            return "";
        }
        
        // 转义特殊的Markdown字符，防止它们被解析为Markdown语法
        // 特别处理以###开头的行，在#前添加反斜杠进行转义
        StringBuilder sb = new StringBuilder();
        String[] lines = text.split("\\r\\n|\\n");
        
        for (String line : lines) {
            // 转义以#开头的行（标题）
            if (line.trim().startsWith("#")) {
                line = line.replaceAll("^(\\s*)(#+)", "$1\\\\$2");
            }
            
            // 转义其他可能影响Markdown格式的字符
            line = line.replace("*", "\\*")
                     .replace("_", "\\_")
                     .replace("`", "\\`")
                     .replace(">", "\\>");
            
            sb.append(line).append("  \n");
        }
        
        return sb.toString();
    }
}
