package com.chargebolt.pheidi.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.chargebolt.pheidi.remote.request.OneSignalSendRequest;
import com.chargebolt.pheidi.remote.response.OneSignalSendResponse;

import so.dian.mofa3.lang.exception.BizProcessException;

import java.util.Map;

@FeignClient(name = "onesignal", url = "https://api.onesignal.com",fallbackFactory = OneSignalApi.OneSignalApiFallbackFactory.class)
public interface OneSignalApi {


    /**
     * Send to specific users via External ID
     * 给指定用户发送消息
     * https://documentation.onesignal.com/reference/push-notification
     * @param header
     * @return
     */
    @PostMapping("/notifications?c=push")
    OneSignalSendResponse sendMessage(@RequestBody OneSignalSendRequest request, @RequestHeader Map<String,String> header);

    @Slf4j
    @Component
    class OneSignalApiFallbackFactory implements FallbackFactory<OneSignalApi> {
        @Override
        public OneSignalApi create(Throwable cause) {
            return new OneSignalApi() {
                @Override
                public OneSignalSendResponse sendMessage(OneSignalSendRequest request, Map<String, String> header) {
                    log.error("OneSignalApi sendMessage error",cause);
                    throw new BizProcessException("调用 OneSignal 发送消息异常");
                }
            };
        }
    }
}
