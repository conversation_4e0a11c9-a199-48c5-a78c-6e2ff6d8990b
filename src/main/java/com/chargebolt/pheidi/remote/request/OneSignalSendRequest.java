package com.chargebolt.pheidi.remote.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class OneSignalSendRequest {

    @JsonProperty("app_id")
    private String appId;
    @JsonProperty("target_channel")
    private String targetChannel;
    /**
     * 标题
     * {"en": "English Title", "es": "Spanish Title"}
     */
    private Map<String,String> headings;
    /**
     * 内容
     * {"en": "English Content", "es": "Spanish Content"}
     */
    private Map<String,String> contents;
    @JsonProperty("include_aliases")
    private IncludeAliases includeAliases;

    /**
     * 链接放这里面的 page 字段里
     */
    @JsonProperty("data")
    private ExtraData extraData;
}
