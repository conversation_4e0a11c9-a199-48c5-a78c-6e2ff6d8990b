package com.chargebolt.pheidi.dao;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.chargebolt.pheidi.domain.MessageChannel;

public interface MessageChannelDao {

    int insertMessageChannel(MessageChannel messageChannel);

    MessageChannel getMessageChannelById(@Param("id") Long id);

    int updateMessageChannelStatus(@Param("id") Long id, @Param("status") Integer status);

    int updateMessageChannel(MessageChannel messageChannel);

    List<MessageChannel> queryPendingChannels(@Param("maxRetryCount") int maxRetryCount, @Param("offset") int offset,
            @Param("limit") int limit);

    void updateRetryCount(@Param("id") Long id, @Param("errMsg") String errMsg,
            @Param("lastAttemptTime") LocalDateTime lastAttemptTime, @Param("gmtUpdate") Long gmtUpdate);

    MessageChannel selectByMessageIdAndChannel(@Param("msgId") Long msgId, @Param("channel") Integer channel);
}
