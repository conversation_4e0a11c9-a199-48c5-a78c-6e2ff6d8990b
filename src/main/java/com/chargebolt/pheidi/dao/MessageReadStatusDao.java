package com.chargebolt.pheidi.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.chargebolt.pheidi.domain.MessageReadStatus;


public interface MessageReadStatusDao {

    void insert(MessageReadStatus readStatus);

    void updateMessageReadStatus(@Param("id") Long id, @Param("status") Integer status);

    List<Map<String, Integer>> countUnreadByBizType(@Param("externalId") String externalId);

        /**
     * 查询每个业务类型的最新消息
     *
     * @param externalId 外部ID
     * @return 最新消息列表
     */
    MessageReadStatus queryLatestMessageByBizType(@Param("externalId") String externalId, @Param("bizType") Integer bizType);

      /**
     * 按组查询消息列表
     *
     * @param externalId 外部ID
     * @param bizType 业务类型
     * @param offset 偏移量
     * @param pageSize 页大小
     * @return 消息列表
     */
    List<MessageReadStatus> queryMessageList(@Param("externalId") String externalId,
                                 @Param("bizType") Integer bizType,
                                 @Param("offset") Integer offset,
                                 @Param("pageSize") Integer pageSize);

    /**
     * 按组统计消息总数
     *
     * @param externalId 外部ID
     * @param bizType 业务类型
     * @return 消息总数
     */
    int countMessage(@Param("externalId") String externalId,
                    @Param("bizType") Integer bizType);


    int unreadNum(@Param("externalId") String externalId);


    /**
     * 批量更新消息状态
     *
     * @param externalId 外部ID
     * @param bizType    业务类型
     * @param status     状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("targetId") String targetId,
            @Param("bizType") Integer bizType,
            @Param("status") Integer status);

    /**
     * 按组查询消息总数
     *
     * @param externalId 外部ID
     * @param bizType 业务类型
     * @return 消息总数
     */
    List<Map<String, Integer>> countTotalByBizType(@Param("externalId") String externalId);
}
