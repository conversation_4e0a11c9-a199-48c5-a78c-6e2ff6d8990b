package com.chargebolt.pheidi.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.chargebolt.pheidi.domain.Message;

public interface MessageDao {

    int insertMessage(Message message);

    void updateMessageStatus(@Param("id") Long id,@Param("status") Integer status);

    void batchInsert(@Param("messages") List<Message> messages);

    Message getById(@Param("messageId") Long messageId);

    List<Message> getByIds(@Param("ids") List<Long> ids);
    
    /**
     * 获取指定ID及更大ID的未推送消息
     * 
     * @param messageId 消息ID
     * @param status 消息状态
     * @return 未推送消息列表
     */
    List<Message> getUnpushedMessagesFromId(@Param("messageId") Long messageId, @Param("status") Integer status);
}
