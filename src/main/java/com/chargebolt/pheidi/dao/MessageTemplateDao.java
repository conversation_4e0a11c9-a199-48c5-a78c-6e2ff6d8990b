package com.chargebolt.pheidi.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.chargebolt.pheidi.domain.MessageTemplate;

public interface MessageTemplateDao {

    MessageTemplate getMessageTemplateByCode(@Param("code") String code);

    MessageTemplate getMessageTemplateByName(@Param("name") String name);

    int updateMessageTemplate(MessageTemplate messageTemplate);

    int insertMessageTemplate(MessageTemplate messageTemplate);

    MessageTemplate getMessageTemplateById(Long id);

    /**
     * 查询消息模板列表
     *
     * @param param 查询参数
     * @return 模板列表
     */
    List<MessageTemplate> queryMessageTemplateList(
            @Param("templateName") String templateName,
            @Param("templateCode") String templateCode,
            @Param("bizType") Integer bizType,
            @Param("status") Integer status,
            @Param("offset") Integer offset,
            @Param("pageSize") Integer pageSize);

    /**
     * 查询消息模板总数
     *
     * @param param 查询参数
     * @return 总数
     */
    int countMessageTemplate(
            @Param("templateName") String templateName,
            @Param("templateCode") String templateCode,
            @Param("bizType") Integer bizType,
            @Param("status") Integer status);

    /**
     * 更新模板状态
     *
     * @param templateId 模板ID
     * @param status     状态
     * @return 更新结果
     */
    int updateMessageTemplateStatus(
            @Param("templateId") Long templateId,
            @Param("status") Integer status);

    /**
     * 查询所有模板
     *
     * @return 模板列表
     */
    List<MessageTemplate> queryAllTemplates();
}
