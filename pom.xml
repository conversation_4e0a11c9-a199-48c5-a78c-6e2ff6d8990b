<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.chargebolt</groupId>
  <artifactId>pheidi</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>pheidi</name>
  <description>海外消息推送系统</description>

  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <spring-cloud.version>Hoxton.SR12</spring-cloud.version>
    <springboot.version>2.3.12.RELEASE</springboot.version>
    <springcloud.version>2.2.9.RELEASE</springcloud.version>
    <jackson.version>2.15.3</jackson.version>
    <mybatis.version>2.1.1</mybatis.version>
    <druid.version>1.2.6</druid.version>
    <feign.version>10.12</feign.version>
    <guava.version>31.1-jre</guava.version>
    <lombok.version>1.18.24</lombok.version>
    <commons-lang3.version>3.9</commons-lang3.version>
    <commons-logging.version>1.2</commons-logging.version>
    <snakeyaml.version>1.26</snakeyaml.version>
    <aspectjweaver.version>1.9.6</aspectjweaver.version>
    <fastjson.version>1.2.83</fastjson.version>
    <httpclient.version>4.5.8</httpclient.version>
    <hdrhistogram.version>2.1.12</hdrhistogram.version>
    <reactive-streams.version>1.0.3</reactive-streams.version>
    <mofa.version>1.0.21-1-RELEASE</mofa.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
    </dependency>
    <!-- Spring Boot 核心依赖 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>so.dian.mofa3</groupId>
      <artifactId>common-lang</artifactId>
      <version>${mofa.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework</groupId>
          <artifactId>spring-webmvc</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>so.dian.mofa3</groupId>
      <artifactId>common-template</artifactId>
      <version>${mofa.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
      <version>${springcloud.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.archaius</groupId>
          <artifactId>archaius-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.aspectj</groupId>
          <artifactId>aspectjweaver</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
      <version>${springcloud.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.servo</groupId>
          <artifactId>servo-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpclient</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.netflix.hystrix</groupId>
          <artifactId>hystrix-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-core</artifactId>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-jackson</artifactId>
      <version>${feign.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
      <version>${mybatis.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-autoconfigure</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
      <version>${druid.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-autoconfigure</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Spring Boot 相关依赖项 -->
    <!-- Web 服务 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-jdbc</artifactId>
    </dependency>

    <!-- 数据库相关依赖项 -->
    <!-- MyBatis 集成 -->
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.33</version>
    </dependency>

    <dependency>
      <groupId>commons-httpclient</groupId>
      <artifactId>commons-httpclient</artifactId>
      <version>3.1</version>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.14</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpmime</artifactId>
      <version>4.5.8</version>
    </dependency>

    <!-- 其他依赖项 -->
    <!-- Lombok，简化Java代码 -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <!-- Fastjson，JSON 处理库 -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
    </dependency>

    <!-- 测试依赖项 -->
    <!-- Spring Boot 测试支持 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-ui</artifactId>
      <version>1.6.15</version>
      <exclusions>
        <exclusion>
          <groupId>org.yaml</groupId>
          <artifactId>snakeyaml</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-autoconfigure</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>pheidi-client</artifactId>
      <version>0.0.4.release</version>
    </dependency>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
      <version>3.15.5</version>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.aventrix.jnanoid</groupId>
      <artifactId>jnanoid</artifactId>
      <version>2.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>${jackson.version}</version> <!-- Use the latest stable version -->
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>${jackson.version}</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>${jackson.version}</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>alibaba-dingtalk-service-sdk</artifactId>
      <version>2.0.0</version>
    </dependency>
    
    <!-- AspectJ 依赖，用于实现AOP功能 -->
    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjweaver</artifactId>
      <version>${aspectjweaver.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>1.14</version>
      </dependency>
      <!-- jsr305 -->
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>3.0.2</version>
      </dependency>
      <!-- Spring Boot BOM -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${springboot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- Spring Cloud BOM -->
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- Jackson Dependencies -->
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <!-- Other Dependencies -->
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>${commons-logging.version}</version>
      </dependency>
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>${snakeyaml.version}</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjweaver</artifactId>
        <version>${aspectjweaver.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hdrhistogram</groupId>
        <artifactId>HdrHistogram</artifactId>
        <version>${hdrhistogram.version}</version>
      </dependency>
      <dependency>
        <groupId>org.reactivestreams</groupId>
        <artifactId>reactive-streams</artifactId>
        <version>${reactive-streams.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>2.15.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.15.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>2.15.3</version>
      </dependency>
      
    </dependencies>
  </dependencyManagement>

  <build>
    <finalName>pheidi</finalName>
    <plugins>
      <!-- ensure jdk 1.8-->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
        <version>3.4</version>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.3.12.RELEASE</version>
        <configuration>
          <mainClass>com.chargebolt.pheidi.AirPushApplication</mainClass>
          <layout>JAR</layout>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>build-info</goal>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.4.1</version>
        <executions>
          <execution>
            <id>enforce</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <dependencyConvergence>
                  <excludes>
                    <exclude>com.alibaba:fastjson</exclude>
                    <exclude>com.aliyun:aliyun-java-sdk-core</exclude>
                    <exclude>com.aliyun:aliyun-java-sdk-sts</exclude>
                    <exclude>com.chargebolt:commons-eden</exclude>
                    <exclude>com.fasterxml.jackson.core:jackson-annotations</exclude>
                    <exclude>com.fasterxml.jackson.core:jackson-core</exclude>
                    <exclude>com.fasterxml.jackson.core:jackson-databind</exclude>
                    <exclude>com.fasterxml:classmate</exclude>
                    <exclude>com.google.api-client:google-api-client</exclude>
                    <exclude>com.google.code.findbugs:jsr305</exclude>
                    <exclude>com.google.code.gson:gson</exclude>
                    <exclude>com.google.guava:guava</exclude>
                    <exclude>com.google.http-client:google-http-client</exclude>
                    <exclude>com.google.http-client:google-http-client-gson</exclude>
                    <exclude>com.google.protobuf:protobuf-java</exclude>
                    <exclude>com.netflix.archaius:archaius-core</exclude>
                    <exclude>com.netflix.hystrix:hystrix-core</exclude>
                    <exclude>com.netflix.ribbon:ribbon-core</exclude>
                    <exclude>com.netflix.ribbon:ribbon-loadbalancer</exclude>
                    <exclude>com.netflix.servo:servo-core</exclude>
                    <exclude>com.squareup.okhttp3:okhttp</exclude>
                    <exclude>com.thoughtworks.xstream:xstream</exclude>
                    <exclude>commons-codec:commons-codec</exclude>
                    <exclude>commons-io:commons-io</exclude>
                    <exclude>commons-lang:commons-lang</exclude>
                    <exclude>commons-logging:commons-logging</exclude>
                    <exclude>io.github.openfeign:feign-core</exclude>
                    <exclude>io.netty:netty-all</exclude>
                    <exclude>io.netty:netty-common</exclude>
                    <exclude>io.netty:netty-handler</exclude>
                    <exclude>io.netty:netty-transport</exclude>
                    <exclude>io.reactivex:rxjava</exclude>
                    <exclude>org.apache.commons:commons-collections4</exclude>
                    <exclude>org.apache.commons:commons-compress</exclude>
                    <exclude>org.apache.commons:commons-lang3</exclude>
                    <exclude>org.apache.commons:commons-pool2</exclude>
                    <exclude>org.apache.httpcomponents:httpclient</exclude>
                    <exclude>org.apache.httpcomponents:httpcore</exclude>
                    <exclude>org.apache.httpcomponents:httpmime</exclude>
                    <exclude>org.aspectj:aspectjweaver</exclude>
                    <exclude>org.bouncycastle:bcpkix-jdk15on</exclude>
                    <exclude>org.hdrhistogram:HdrHistogram</exclude>
                    <exclude>org.projectlombok:lombok</exclude>
                    <exclude>org.reactivestreams:reactive-streams</exclude>
                    <exclude>org.slf4j:jcl-over-slf4j</exclude>
                    <exclude>org.slf4j:slf4j-api</exclude>
                    <exclude>org.springframework.boot:spring-boot-autoconfigure</exclude>
                    <exclude>org.springframework.boot:spring-boot-starter</exclude>
                    <exclude>org.springframework.boot:spring-boot-starter-jdbc</exclude>
                    <exclude>org.springframework:spring-aop</exclude>
                    <exclude>org.springframework:spring-beans</exclude>
                    <exclude>org.springframework:spring-context</exclude>
                    <exclude>org.springframework:spring-core</exclude>
                    <exclude>org.springframework:spring-tx</exclude>
                    <exclude>org.springframework:spring-web</exclude>
                    <exclude>org.yaml:snakeyaml</exclude>
                  </excludes>
                </dependencyConvergence>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.4.1</version>
        <executions>
          <execution>
            <id>enforce-versions</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <dependencyConvergence>
                  <excludes>
                    <exclude>commons-codec:commons-codec</exclude>
                  </excludes>
                </dependencyConvergence>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>image</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>com.google.cloud.tools</groupId>
            <artifactId>jib-maven-plugin</artifactId>
            <version>3.4.1</version>
            <configuration>
              <containerizingMode>packaged</containerizingMode>
              <container>
                <entrypoint>/opt/jboss/container/java/run/run-java.sh</entrypoint>
              </container>
              <extraDirectories>
                <paths>
                  <path>
                    <from>target/</from>
                    <includes>${project.build.finalName}.jar</includes>
                    <into>/deployments</into>
                  </path>
                </paths>
              </extraDirectories>
              <pluginExtensions>
                <pluginExtension>
                  <implementation>
                    com.google.cloud.tools.jib.maven.extension.layerfilter.JibLayerFilterExtension</implementation>
                  <configuration
                    implementation="com.google.cloud.tools.jib.maven.extension.layerfilter.Configuration">
                    <filters>
                      <filter>
                        <!-- exclude all jib layers, which is basically anything in /app -->
                        <glob>/app/**</glob>
                      </filter>
                      <filter>
                        <!-- this is our fat jar, this should be kept by adding it into its own
                        layer -->
                        <glob>/deployments/${project.build.finalName}.jar</glob>
                        <toLayer>jib-custom-fatJar</toLayer>
                      </filter>
                    </filters>
                  </configuration>
                </pluginExtension>
              </pluginExtensions>
              <from>
                <image>
                  quay.xiaodiankeji.net/openjdk/openjdk-8-runtime@sha256:241d076fd757a1fecfd10fd7dc7d0bd16bd0f9d8228a22433d6cf2c2ca3cb8f3</image>
                <platforms>
                  <platform>
                    <architecture>arm64</architecture>
                    <os>linux</os>
                  </platform>
                  <platform>
                    <architecture>amd64</architecture>
                    <os>linux</os>
                  </platform>
                </platforms>
              </from>
              <to>
                <image>quay.xiaodiankeji.net/dian-dev/${project.build.finalName}</image>
                <auth>
                  <username>${env.REGISTRY_USR}</username>
                  <password>${env.REGISTRY_PSW}</password>
                </auth>
              </to>
            </configuration>
            <dependencies>
              <dependency>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-layer-filter-extension-maven</artifactId>
                <version>0.3.0</version>
              </dependency>
            </dependencies>
          </plugin>
          <plugin>
            <groupId>com.diffplug.spotless</groupId>
            <artifactId>spotless-maven-plugin</artifactId>
            <version>2.43.0</version>
            <configuration>
              <ratchetFrom>origin/main</ratchetFrom>
              <formats>
                <format>
                  <includes>
                    <include>*.md</include>
                    <include>.gitignore</include>
                  </includes>
                  <trimTrailingWhitespace></trimTrailingWhitespace>
                  <endWithNewline></endWithNewline>
                  <indent>
                    <tabs>true</tabs>
                    <spacesPerTab>4</spacesPerTab>
                  </indent>
                </format>
              </formats>
              <java>
                <includes>
                  <include>src/main/java/**/*.java</include>
                  <include>src/test/java/**/*.java</include>
                </includes>
                <googleJavaFormat>
                  <style>GOOGLE</style>
                  <reflowLongStrings>true</reflowLongStrings>
                </googleJavaFormat>
                <importOrder>
                  <wildcardsLast>false</wildcardsLast>
                  <order>java|javax|jakarta,org.springframework,org,com,so.dian,,\#so.dian,\#</order>
                  <semanticSort>false</semanticSort>
                </importOrder>
                <removeUnusedImports></removeUnusedImports>
                <formatAnnotations></formatAnnotations>
              </java>
              <pom>
                <includes>
                  <include>pom.xml</include>
                </includes>
                <sortPom></sortPom>
              </pom>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
            <version>3.21.2</version>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>3.5.0</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>3.21.2</version>
        <reportSets>
          <reportSet>
            <id>aggregate</id>
            <!-- don't run aggregate in child modules -->
            <reports>
              <report>aggregate-pmd</report>
              <report>aggregate-cpd</report>
            </reports>
            <inherited>false</inherited>
          </reportSet>
          <reportSet>
            <id>default</id>
            <reports>
              <report>pmd</report>
              <report>cpd</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>
</project>